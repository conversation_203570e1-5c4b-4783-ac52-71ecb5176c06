# Move Hardcoded Text to Constants - TODO

## Phase 1: Expand AppStrings.ts
- [ ] Add all missing hardcoded text strings to AppStrings.ts
- [ ] Organize constants by category (UI, Messages, Labels, etc.)

## Phase 2: Update Component Files
- [ ] Update CommonCardStyle.tsx - replace "SORR<PERSON>", "No Data Found"
- [ ] Update DrawerMenu.tsx - replace "v1.5.4", "LOGOUT"
- [ ] Update CommonDropdown.tsx - replace "Cancel"
- [ ] Update other common components

## Phase 3: Update Screen Files
- [ ] Update BookAppointmentsScreen.tsx - replace all hardcoded strings
- [ ] Update MechanicProfilePage.tsx - replace all hardcoded strings
- [ ] Update other screen files

## Phase 4: Testing and Verification
- [ ] Verify all changes compile correctly
- [ ] Test application functionality
- [ ] Check for any missed hardcoded strings
