/**
 * Author: <PERSON><PERSON><PERSON>
 * Date: 03/06/2025
 * Last Update: 03/06/2025
 *
 * @format
 */
import React, {useEffect} from 'react';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import AppNavigator from './src/navigations/AppNavigator';
import {AuthProvider} from './src/utils/configs/AuthContext';

import {createStackNavigator} from '@react-navigation/stack';
import {navigationRef} from './src/navigations/navigationRef';
import {Alert, BackHandler} from 'react-native';

const Stack = createStackNavigator();
import initializeFirebase from './src/utils/configs/firebaseConfig';

function App(): React.ReactElement {
  useEffect(() => {
    TODO: 'Check device back button logic to captch and navigate back to previous screen';
    const backAction = () => {
      if (navigationRef.current?.canGoBack()) {
        navigationRef.current?.goBack();
        return true; // handled
      } else {
        Alert.alert('Exit App', 'Do you want to exit?', [
          {text: 'Cancel', style: 'cancel', onPress: () => null},
          {text: 'Yes', onPress: () => BackHandler.exitApp()},
        ]);
        return true; // prevent default
      }
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, []);
  useEffect(() => {
    const init = async () => {
      try {
        await initializeFirebase();
      } catch (error) {
        console.error('Failed to initialize Firebase:', error);
      }
    };
    init();
  }, []);

  return (
    <AuthProvider>
      <SafeAreaProvider>
        <GestureHandlerRootView style={styles.container}>
          <AppNavigator navigationRef={navigationRef} />
        </GestureHandlerRootView>
      </SafeAreaProvider>
    </AuthProvider>
  );
}

const styles = {
  container: {
    flex: 1,
  },
};

export default App;
