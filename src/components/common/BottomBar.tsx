import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Image} from 'react-native';
import {BottomTabBarProps} from '@react-navigation/bottom-tabs';
import BottomTabs from '../../utils/constants/BottomTabs';
import {Colors} from '../../utils/constants/Theme';

const BottomBar: React.FC<BottomTabBarProps> = ({state, navigation}) => (
  <View style={styles.container}>
    {state.routes.map((route, index) => {
      const isFocused = state.index === index;
      const tab = BottomTabs.find(t => t.key === route.name);

      if (!tab) return null;

      const onPress = () => {
        const event = navigation.emit({
          type: 'tabPress',
          target: route.key,
          canPreventDefault: true,
        });

        if (!isFocused && !event.defaultPrevented) {
          navigation.navigate(route.name);
        }
      };

      return (
        <TouchableOpacity
          key={route.key}
          accessibilityRole="button"
          accessibilityState={isFocused ? {selected: true} : {}}
          onPress={onPress}
          style={[
            styles.tab,
            isFocused && {backgroundColor: Colors.TAB_BAR_BG_INACTIVE_COLOR}, // highlight active tab
          ]}
          activeOpacity={0.5}>
          <Image
            source={tab.icon}
            style={[
              styles.icon,
              {
                tintColor: isFocused
                  ? Colors.PRIMARY
                  : Colors.TAB_IN_ACTIVE_COLOR,
              },
            ]}
            resizeMode="contain"
          />
          <Text
            style={[
              styles.label,
              {color: isFocused ? Colors.PRIMARY : Colors.TAB_IN_ACTIVE_COLOR},
            ]}>
            {tab.label}
          </Text>
        </TouchableOpacity>
      );
    })}
  </View>
);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: Colors.TAB_BAR_BG_COLOR,
    alignItems: 'center',
    height: 60,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 6,
    borderWidth: 1,
    alignSelf: 'stretch',
    borderColor: Colors.TAB_BORDER_COLOR,
    height: '100%',
  },
  icon: {
    width: 28,
    height: 28,
    marginBottom: 2,
  },
  label: {
    fontSize: 8,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
});

export default BottomBar;
/* import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Animated,
} from 'react-native';
import {BottomTabBarProps} from '@react-navigation/bottom-tabs';
import BottomTabs from '../../utils/constants/BottomTabs';
import {Colors} from '../../utils/constants/Theme';

const BottomBar: React.FC<BottomTabBarProps> = ({state, navigation}) => {
  const animatedValues = React.useRef(
    state.routes.map(() => new Animated.Value(0)),
  ).current;

  React.useEffect(() => {
    animatedValues.forEach((value, index) => {
      Animated.timing(value, {
        toValue: state.index === index ? 1 : 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    });
  }, [state.index, animatedValues]);

  return (
    <View style={styles.container}>
      <View style={styles.tabBar}>
        {state.routes.map((route, index) => {
          const isFocused = state.index === index;
          const tab = BottomTabs.find(t => t.key === route.name);

          if (!tab) return null;

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          const scale = animatedValues[index].interpolate({
            inputRange: [0, 1],
            outputRange: [1, 1.1],
          });

          const translateY = animatedValues[index].interpolate({
            inputRange: [0, 1],
            outputRange: [0, -2],
          });

          const iconOpacity = animatedValues[index].interpolate({
            inputRange: [0, 1],
            outputRange: [0.6, 1],
          });

          return (
            <TouchableOpacity
              key={route.key}
              accessibilityRole="button"
              accessibilityState={isFocused ? {selected: true} : {}}
              onPress={onPress}
              style={styles.tabButton}
              activeOpacity={0.7}>
              <Animated.View
                style={[
                  styles.tabContent,
                  {
                    transform: [{scale}, {translateY}],
                  },
                ]}>
                {isFocused && (
                  <Animated.View
                    style={[
                      styles.activeBackground,
                      {
                        opacity: animatedValues[index],
                      },
                    ]}
                  />
                )}

                <Animated.View
                  style={[
                    styles.iconContainer,
                    isFocused && styles.activeIconContainer,
                    {opacity: iconOpacity},
                  ]}>
                  <Image
                    source={tab.icon}
                    style={[
                      styles.icon,
                      {
                        tintColor: isFocused
                          ? '#ffffff'
                          : Colors.TAB_IN_ACTIVE_COLOR,
                      },
                    ]}
                    resizeMode="contain"
                  />

                  {isFocused && (
                    <Animated.View
                      style={[
                        styles.glowEffect,
                        {
                          opacity: animatedValues[index].interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, 0.8],
                          }),
                        },
                      ]}
                    />
                  )}
                </Animated.View>

                <Animated.Text
                  style={[
                    styles.label,
                    {
                      color: isFocused ? '#ffffff' : Colors.TAB_IN_ACTIVE_COLOR,
                      opacity: animatedValues[index].interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.7, 1],
                      }),
                      fontWeight: isFocused ? '700' : '600',
                    },
                  ]}>
                  {tab.label}
                </Animated.Text>

                {isFocused && (
                  <Animated.View
                    style={[
                      //styles.activeDot,
                      {
                        opacity: animatedValues[index],
                        transform: [
                          {
                            scale: animatedValues[index].interpolate({
                              inputRange: [0, 1],
                              outputRange: [0, 1],
                            }),
                          },
                        ],
                      },
                    ]}
                  />
                )}
              </Animated.View>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingBottom: 25,
    paddingTop: 10,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: 'rgba(26, 26, 46, 0.95)',
    borderRadius: 25,
    paddingVertical: 8,
    paddingHorizontal: 8,
    // Glassmorphism effect
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    // Modern shadow system
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 20,
    // Blur effect backdrop
    // backdropFilter: 'blur(20px)', // Not supported in React Native
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    position: 'relative',
  },
  activeBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 107, 107, 0.3)',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 107, 107, 0.4)',
  },
  iconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  activeIconContainer: {
    // Additional styling for active icon if needed
  },
  icon: {
    width: 24,
    height: 24,
  },
  glowEffect: {
    position: 'absolute',
    top: -8,
    left: -8,
    right: -8,
    bottom: -8,
    backgroundColor: 'rgba(255, 107, 107, 0.3)',
    borderRadius: 20,
    // Blur effect for glow
    shadowColor: '#ff6b6b',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.6,
    shadowRadius: 10,
    elevation: 5,
  },
  label: {
    fontSize: 11,
    letterSpacing: 0.5,
    textAlign: 'center',
  },
  activeDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#ffffff',
    marginTop: 2,
    // Subtle glow for the dot
    shadowColor: '#ffffff',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.8,
    shadowRadius: 3,
    elevation: 3,
  },
});

export default BottomBar;
 */
