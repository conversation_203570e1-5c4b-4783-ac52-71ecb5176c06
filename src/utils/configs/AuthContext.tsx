import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { FirebaseAuthTypes } from '@react-native-firebase/auth';
import { getAuth, onAuthStateChanged, createUserWithEmailAndPassword, signInWithEmailAndPassword } from '@react-native-firebase/auth';
import { getDatabase } from '@react-native-firebase/database';
import { getAnalytics, logEvent } from '@react-native-firebase/analytics';
import { getApp } from '@react-native-firebase/app';

interface UserRegistrationData {
  email: string;
  firstName: string;
  lastName: string;
  mobile: string;
  providerType: string;
  providerTypeId: string;
  registrationStatus: string;
}

interface AuthContextType {
  user: FirebaseAuthTypes.User | null;
  loading: boolean;
  signUp: (email: string, password: string) => Promise<FirebaseAuthTypes.User>;
  signIn: (email: string, password: string) => Promise<FirebaseAuthTypes.User>;
  signOut: () => Promise<void>;
  isRegisteredAsMechanicUser: (userId: string) => Promise<boolean>;
  resetPassword: (email: string) => Promise<void>;
  registerUser: (
    email: string,
    password: string,
    userData: Omit<UserRegistrationData, 'email'>,
  ) => Promise<string>;
  updateUserRegistration: (
    customerId: string,
    firstname: string,
    lastname: string,
    email: string,
    phoneNumber: string,
    providerType: string,
    providerTypeId: string,
    registrationStatus: string,
  ) => Promise<void>;
  updateRegistrationStatus: (
    customerId: string,
    registrationStatus: string,
  ) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({children}: AuthProviderProps) => {
  const [user, setUser] = useState<FirebaseAuthTypes.User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const app = getApp();
    const auth = getAuth(app);
    const analytics = getAnalytics(app);
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser: FirebaseAuthTypes.User | null) => {
      setUser(firebaseUser);
      setLoading(false);
      if (firebaseUser) {
        logEvent(analytics, 'user_login', {
          userId: firebaseUser.uid,
          method: firebaseUser.providerData[0]?.providerId || 'email',
        });
      }
    });

    return unsubscribe;
  }, []);

  const signUp = async (email: string, password: string) => {
    try {
      const app = getApp();
      const auth = getAuth(app);
      const analytics = getAnalytics(app);
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      await logEvent(analytics, 'user_signup', {
        method: 'email',
        userId: userCredential.user.uid,
      });
      return userCredential.user;
    } catch (error) {
      throw error;
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const auth = getAuth(getApp());
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return userCredential.user;
    } catch (error) {
      throw error;
    }
  };

  const isRegisteredAsMechanicUser = async (userId: string) => {
    const snapshot = await database()
      .ref('mechanic')
      .child(userId)
      .once('value');
    return snapshot.exists();
  };

  const signOut = async () => {
    try {
      const app = getApp();
      const auth = getAuth(app);
      const analytics = getAnalytics(app);
      await auth.signOut();
      await logEvent(analytics, 'user_logout');
    } catch (error) {
      throw error;
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const auth = getAuth(getApp());
      await auth.sendPasswordResetEmail(email);
    } catch (error) {
      throw error;
    }
  };

  const registerUser = async (
    email: string,
    password: string,
    userData: Omit<UserRegistrationData, 'email'>,
  ) => {
    try {
      const app = getApp();
      const auth = getAuth(app);
      const database = getDatabase(app);
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const uid = userCredential.user.uid;
      await database.ref(`customer/${uid}`).set({
        email: email,
        'first-name': userData.firstName,
        'last-name': userData.lastName,
        mobile: userData.mobile,
        'provider-type': userData.providerType,
        'provider-type-id': userData.providerTypeId,
        'registration-status': userData.registrationStatus,
      });
      return uid;
    } catch (error) {
      throw error;
    }
  };

  const updateUserRegistration = async (
    customerId: string,
    firstname: string,
    lastname: string,
    email: string,
    phoneNumber: string,
    providerType: string,
    providerTypeId: string,
    registrationStatus: string,
  ) => {
    const database = getDatabase(getApp());
    return database.ref(`customer/${customerId}`).set({
      email: email,
      'first-name': firstname,
      'last-name': lastname,
      mobile: phoneNumber,
      'provider-type': providerType,
      'provider-type-id': providerTypeId,
      'registration-status': registrationStatus,
    });
  };

  const updateRegistrationStatus = async (
    customerId: string,
    registrationStatus: string,
  ) => {
    const database = getDatabase(getApp());
    return database.ref(`customer/${customerId}/registration-status`).set(registrationStatus);
  };

  const value: AuthContextType = {
    user,
    loading,
    signUp,
    signIn,
    isRegisteredAsMechanicUser,
    signOut,
    resetPassword,
    registerUser,
    updateUserRegistration,
    updateRegistrationStatus,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
