import { ref, query, orderByChild, equalTo, get, set, update, push, remove } from '@react-native-firebase/database';
import storage from '@react-native-firebase/storage';
import { getApp } from '@react-native-firebase/app';
import { DropdownOption } from '../configs/types';

interface VehicleData {
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
  warning?: string;
}

type Location = {
  latitude: number;
  longitude: number;
};

interface WorkRequestData {
  alert: string;
  'created-time': number;
  customer: string;
  latitude: number;
  longitude: number;
  mechanic: string;
  notes: {
    images: string;
    'notes-content': string;
  };
  'request-address': {
    address_array: {
      address1: string;
      address2?: string;
      city?: string;
      country?: string;
      state?: string;
      zipcode?: string;
    };
    formatted_address: string;
    location: {
      latitude: number;
      longitude: number;
    };
  };
  'request-date': string;
  'request-time-range': string;
  'request-type': string;
  'screening-FAQ': Record<string, any>;
  services: Record<string, {
    'customer-bid': string[];
    'mechanic-bid': string[];
    'service-type': string;
    'sub-services': string[];
  }>;
  status: string;
  vehicle: string;
}

export const VehicleService = {
  getVehicles: async (customerId: string): Promise<DropdownOption[]> => {
    try {
      const snapshot = await get(ref(getApp().database(), `customer/${customerId}/myvehicles`));
      const vehicles = snapshot.val();
      if (vehicles) {
        return Object.keys(vehicles).map(key => {
          const vehicle = vehicles[key];
          const label = vehicle.name || `${vehicle.make || ''} ${vehicle.model || ''}`.trim() || key;
          return { label, value: key };
        });
      }
      return [];
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      return [];
    }
  },

  addVehicleInfo: async (customerId: string, vehicleData: VehicleData) => {
    try {
      const newRef = push(ref(getApp().database(), `customer/${customerId}/myvehicles`));
      await set(newRef, vehicleData);
      return newRef;
    } catch (error) {
      console.error('Error adding vehicle:', error);
      throw error;
    }
  },

  removeVehicleInfo: async (customerId: string, vehicleId: string) => {
    try {
      return await remove(ref(getApp().database(), `customer/${customerId}/myvehicles/${vehicleId}`));
    } catch (error) {
      console.error('Error removing vehicle:', error);
      throw error;
    }
  },

  getVehicleServices: () => {
    return ref(getApp().database(), 'service');
  },

  getServiceIdNameMap: async (): Promise<Record<string, string>> => {
    try {
      const snapshot = await get(ref(getApp().database(), 'service'));
      const serviceData = snapshot.val();
      const map: Record<string, string> = {};
      if (serviceData) {
        Object.keys(serviceData).forEach(key => {
          const service = serviceData[key];
          map[key] = service.name || key;
        });
      }
      return map;
    } catch (error) {
      console.error('Error fetching service map:', error);
      return {};
    }
  },

  getSubServiceIdNameMap: async (): Promise<Record<string, string>> => {
    try {
      const snapshot = await get(ref(getApp().database(), 'sub-service'));
      const subServiceData = snapshot.val();
      const map: Record<string, string> = {};
      if (subServiceData) {
        Object.keys(subServiceData).forEach(key => {
          const subService = subServiceData[key];
          map[key] = subService.name || key;
        });
      }
      return map;
    } catch (error) {
      console.error('Error fetching sub-service map:', error);
      return {};
    }
  },

  getSubServicePriceData: async (): Promise<Record<string, any>> => {
    try {
      const snapshot = await get(ref(getApp().database(), 'sub-service'));
      const subServiceData = snapshot.val();
      return subServiceData || {};
    } catch (error) {
      console.error('Error fetching sub-service price data:', error);
      return {};
    }
  },

  getPendingRequestData: (customerId: string) => {
    return query(
      ref(getApp().database(), `customer/${customerId}/work-requests`),
      orderByChild('status'),
      equalTo('pending')
    );
  },

  getMultiplePendingRequest: (customerId: string) => {
    return query(
      ref(getApp().database(), 'multiple-request'),
      orderByChild('customer'),
      equalTo(customerId)
    );
  },

  getAppointments: (customerId: string) => {
    return query(
      ref(getApp().database(), `customer/${customerId}/appointments`),
      orderByChild('status'),
      equalTo('new')
    );
  },

  getLoggedMechanics: () => {
    return query(
      ref(getApp().database(), 'mechanic'),
      orderByChild('login-status'),
      equalTo(true)
    );
  },

  getPreScreeningQuestions: () => {
    return ref(getApp().database(), 'screening-questions');
  },

  getAlertConfirmation: async (): Promise<DropdownOption[]> => {
    try {
      const snapshot = await get(ref(getApp().database(), 'alert'));
      const alertData = snapshot.val();
      if (alertData) {
        const options = Object.keys(alertData).map(key => {
          const alertItem = alertData[key];
          const alertValue = alertItem.alert || alertItem.label || alertItem.name || key;
          const numValue = parseInt(alertValue, 10) || 0;
          let label: string;
          if (numValue >= 60) {
            const hours = numValue / 60;
            label = `${hours} hour${hours !== 1 ? 's' : ''} before appointment`;
          } else {
            label = `${alertValue} minutes before appointment`;
          }
          return { label, value: key, sortValue: numValue };
        });
        return options.sort((a, b) => a.sortValue - b.sortValue);
      }
      return [];
    } catch (error) {
      console.error('Error fetching alert confirmations:', error);
      return [];
    }
  },

  updateCustomerLocation: async (customerId: string, location: Location) => {
    try {
      return await set(
        ref(getApp().database(), `customer/${customerId}/current-location`),
        { latitude: location.latitude, longitude: location.longitude }
      );
    } catch (error) {
      console.error('Error updating customer location:', error);
      throw error;
    }
  },

  createWorkRequest: async (workRequestData: WorkRequestData): Promise<string> => {
    try {
      const db = getApp().database();
      const workRequestRef = push(ref(db, 'work-request'));
      await set(workRequestRef, workRequestData);

      const workRequestId = workRequestRef.key || 'unknown';

      await Promise.all([
        set(ref(db, `customer/${workRequestData.customer}/work-requests/${workRequestId}/status`), 'pending'),
        set(ref(db, `mechanic/${workRequestData.mechanic}/work-requests/${workRequestId}/status`), 'pending'),
      ]);

      return workRequestId;
    } catch (error) {
      console.error('Error creating work request:', error);
      throw error;
    }
  },

  updateWorkRequestStatus: async (workRequestId: string, status: string): Promise<void> => {
    try {
      await set(ref(getApp().database(), `work-request/${workRequestId}/status`), status);
    } catch (error) {
      console.error('Error updating work request status:', error);
      throw error;
    }
  },

  getWorkRequests: (customerId: string) => {
    return query(
      ref(getApp().database(), 'work-request'),
      orderByChild('customer'),
      equalTo(customerId)
    );
  },

  getWorkRequestById: (workRequestId: string) => {
    return ref(getApp().database(), `work-request/${workRequestId}`);
  },

  uploadImageToStorage: async (imageUri: string, folder: string = 'work-request-images'): Promise<string> => {
    try {
      const filename = `${Date.now()}_${Math.random().toString(36).substring(7)}.jpg`;
      const reference = storage().ref(`${folder}/${filename}`);

      await reference.putFile(imageUri);
      const downloadURL = await reference.getDownloadURL();

      return downloadURL;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  },

  uploadMultipleImages: async (imageUris: string[], folder: string = 'work-request-images'): Promise<string[]> => {
    try {
      const uploadPromises = imageUris.map(uri => VehicleService.uploadImageToStorage(uri, folder));
      const downloadURLs = await Promise.all(uploadPromises);
      return downloadURLs;
    } catch (error) {
      console.error('Error uploading multiple images:', error);
      throw error;
    }
  },

  fetchSavedLocations: async (customerId: string): Promise<any[]> => {
    try {
      const snapshot = await get(ref(getApp().database(), `customer/${customerId}/saved-locations`));
      const locations = snapshot.val();
      if (locations) {
        return Object.keys(locations).map(key => ({ id: key, ...locations[key] }));
      }
      return [];
    } catch (error) {
      console.error('Error fetching saved locations:', error);
      return [];
    }
  },

  saveLocation: async (customerId: string, locationData: {
    name: string;
    address: string;
    coordinates: { latitude: number; longitude: number };
  }): Promise<string> => {
    try {
      const newLocationRef = push(ref(getApp().database(), `customer/${customerId}/saved-locations`));
      await set(newLocationRef, {
        name: locationData.name,
        address: locationData.address,
        coordinates: locationData.coordinates,
      });
      return newLocationRef.key || 'unknown';
    } catch (error) {
      console.error('Error saving location:', error);
      throw error;
    }
  },

  updateSavedLocation: async (customerId: string, locationId: string, locationData: {
    name: string;
    address: string;
    coordinates: { latitude: number; longitude: number };
  }): Promise<void> => {
    try {
      await update(
        ref(getApp().database(), `customer/${customerId}/saved-locations/${locationId}`),
        {
          name: locationData.name,
          address: locationData.address,
          coordinates: locationData.coordinates,
        }
      );
    } catch (error) {
      console.error('Error updating saved location:', error);
      throw error;
    }
  },

  deleteSavedLocation: async (customerId: string, locationId: string): Promise<void> => {
    try {
      await remove(ref(getApp().database(), `customer/${customerId}/saved-locations/${locationId}`));
    } catch (error) {
      console.error('Error deleting saved location:', error);
      throw error;
    }
  },
};
