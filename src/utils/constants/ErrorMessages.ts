/*
  Copyright (c) 2023 myCANx
  All rights reserved.

  This software contains confidential and proprietary information
  of myCANx. Any reproduction or distribution in whole or
  in part is strictly prohibited without the prior written consent
  of myCANx.
*/

/* Here write all error messages */
export const ERROR_MESSAGE_FOR_PASSWORD =
  'Passwords must be at least 6 characters long';
export const ERROR_MESSAGE_FOR_CONIRM_PASSWORD =
  'Password must match with confirm password.';
export const ERROR_MESSAGE_FOR_EMAIL = 'Enter a valid email address.';
export const ERROR_MESSAGE_FOR_FIRSTNAME = 'Enter a first name.';
export const ERROR_MESSAGE_FOR_LASTNAME = 'Enter a last name.';
export const ERROR_MESSAGE_FOR_MOBILE = 'Enter a valid mobile number.';
export const ERROR_MESSAGE_FOR_VEHICLE_MILEAGE = 'Enter your vehicle mileage.';
export const ERROR_MESSAGE_FOR_VEHICLE_CATEGORY = 'Choose vehicle type.';
export const ERROR_MESSAGE_FOR_YOUR_VEHICLE = 'Choose my vehicle.';
export const ERROR_MESSAGE_FOR_FETCH_VEHICLE =
  'Choose location you want to book the service.';

export const ERROR_MESSAGE_FOR_ALLREADY_REGISTERED =
  'This email is already registered.';
export const ERROR_MESSAGE_FOR_VEHICLE_SERVICE = 'Choose vehicle service.';
export const ERROR_MESSAGE_FOR_VEHICLE_SUB_SERVICE =
  'Choose vehicle sub service type.';
export const ERROR_MESSAGE_FOR_VEHICLE_TIME = 'Choose service duration.';
export const ERROR_MESSAGE_FOR_VEHICLE_DATE = 'Choose date for service.';
export const ERROR_MESSAGE_FOR_VEHICLE_ALERT = 'Choose alert for service.';

export const ERROR_MESSAGE_FOR_REVIEW = {
  punctuality: [{type: 'required', message: 'Choose punctuality for review.'}],
  quality: [{type: 'required', message: 'Choose quality for review.'}],
  speed: [{type: 'required', message: 'Choose speed for review.'}],
  rating: [{type: 'required', message: 'Select rating for review.'}],
};

export const ERROR_MESSAGE_FOR_SIGNUP = {
  firstname: [{type: 'required', message: 'Enter first name.'}],
  lastname: [{type: 'required', message: 'Enter last name.'}],
  email: [
    {type: 'required', message: 'Enter a email.'},
    {type: 'pattern', message: 'Your Email must match valid email format.'},
    {type: 'validEmail', message: 'This email is already registered.'},
  ],
  mobile: [
    {type: 'required', message: 'Enter a mobile number.'},
    {
      type: 'minlength',
      message: 'Mobile number must be at least 10 characters long.',
    },
  ],
  password: [
    {type: 'required', message: 'Enter a password.'},
    {
      type: 'minlength',
      message: 'Password must be at least 6 characters long.',
    },
    {
      type: 'maxlength',
      message: 'Password cannot be more than 25 characters long.',
    },
  ],
  confirmpassword: [
    {type: 'mismatch', message: 'Password must match with confirm password.'},
  ],
  vehicletype: [
    {type: 'required', message: 'Choose vehicle type.'},
    {type: 'nodata', message: 'Choose another vehicle type.'},
  ],
  vehiclemake: [
    {type: 'required', message: 'Choose vehicle make.'},
    {type: 'nodata', message: 'Choose another vehicle type.'},
  ],
  vehiclemodel: [
    {type: 'required', message: 'Choose vehicle model.'},
    {type: 'nodata', message: 'Choose another vehicle make.'},
  ],
  vehicleyear: [
    {type: 'required', message: 'Choose vehicle year.'},
    {type: 'nodata', message: 'Choose another vehicle model.'},
  ],
  vehicletrim: [
    {type: 'required', message: 'Choose vehicle trim.'},
    {type: 'nodata', message: 'Choose another vehicle model.'},
  ],
  vehiclemileage: [
    {type: 'required', message: 'Enter vehicle mileage.'},
    {
      type: 'maxlength',
      message: 'Vehicle mileage cannot be more than 5 digit long.',
    },
    {type: 'pattern', message: 'Vehicle mileage allow only number.'},
  ],
  fueltype: [{type: 'required', message: 'Choose fuel type.'}],
  gender: [{type: 'required', message: 'Choose gender.'}],
  maritalstatus: [{type: 'required', message: 'Choose marital status.'}],
  dateofbirth: [{type: 'required', message: 'Choose date of birth.'}],
  locationchoose: [{type: 'required', message: 'Choose location near you.'}],
  address1: [{type: 'required', message: 'Enter address1.'}],
  address2: [{type: 'required', message: 'Enter address2.'}],
  city: [{type: 'required', message: 'Enter city.'}],
  state: [{type: 'required', message: 'Enter state.'}],
  zipcode: [{type: 'required', message: 'Enter zipcode.'}],
  country: [{type: 'required', message: 'Enter country.'}],
  vin: [
    {type: 'required', message: 'Enter valid VIN.'},
    {type: 'pattern', message: 'VIN can contain alphabets and numbers only.'},
    {type: 'minlength', message: 'VIN must be 17 characters long.'},
    {type: 'maxlength', message: 'VIN must be 17 characters long.'},
  ],
};
