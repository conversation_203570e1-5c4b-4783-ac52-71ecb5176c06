import { hexToRGB<PERSON> } from '../helpers/ColorUtils';


export const Colors = {
  PRIMARY: '#a10000',
  SECONDARY: '#0D1114',
  BACKGROUND: '#fff',
  BUTTON_COLOR: '#a10000',
  BUTTON_TEXT_COLOR: '#fff',
  BORDER_COLOR: '#a10000',
  TEXT_COLOR: '#a10000',
  PRIMARY_DARK:'#661518',
  PRIMARY_OPACITY_1: hexToRGBA('#a10000', 0.90),
//Login Main Screen colors
  TERMS_TEXT_COLOR: '#444',
  LINK_TEXT_COLOR: '#a10000',
  DIVIDER_COLOR: 'darkgrey',
  LOGIN_INPUT_TEXT_BG_COLOR: '#9D9C9D',
  BOTTOM_SIGNUP_TEXT_COLOR: '#9E9E9E',
//bottom nav tabs colors
  TAB_IN_ACTIVE_COLOR: '#888888',
  TAB_ACTIVE_COLOR: '#a10000',
  TAB_BAR_BG_COLOR: 'rgb(14,18,20)',
  TAB_BAR_BG_INACTIVE_COLOR: '#1b2128',
  TAB_BORDER_COLOR: '#1B2227',
//Dashboard screen colors
  DASHBOARD_CARD_NAME_TEXT: 'black',
//Appbar colors
  APPBAR_BG_COLOR: '#2C3A41',
//DRopDown colors
  DROPDOWN_BORDER_COLOR: 'black',
  COMMON_DROP_DOWN_TEXT_COLOR:'#373c49',
//Common component colors
  COMMON_GREY_SHADE_DARK: '#373c49',
  COMMON_GREY_SHADE_LIGHT: '#888888',
  COMMON_GREY_SHADOW_LIGHT_COLOR:'#F6F6F6',
  COMMON_WHITE_SHADE: '#f5f5f5',
  COMMON_BlACK_SHADE: '#000000',
  COMMON_HEADING_COLOR_1: '#888888',
  COMMON_COMPONENT_TEXT_COLOR:'#666666',
  COMMON_TAB_SECTION_BG_COLOR:'#5A6A77',
  COMMON_DRAWER_HEADER_TITLE_BG_COLOR:'#2E3A48',
  COMMON_DRAWER_ICON_CONTAINER_BG_COLOR:'#151e25',
};
export const Fonts = {
  ROBO_REGULAR: 'Roboto-Regular',
  ROBO_BOLD: 'Roboto-Bold',
  ROBO_MEDIUM: 'Roboto-Medium',
  ROBO_LIGHT: 'Roboto-Light',
  ROBO_THIN: 'Roboto-Thin',
};
export const Sizes = {
  XSMALL: 10,
  SMALL: 12,
  MEDIUM: 14,
  LARGE: 16,
  XLARGE: 18,
  XXLARGE: 20,
  XXXLARGE: 28,
};
export const CommonUIParams = {
  CUSTOM_PADDING_8:8,
  CUSTOM_PADDING_12:12,
  CUSTOM_PADDING_14:14,
  CUSTOM_PADDING_16:16,
  CUSTOM_APP_BAR_TITLE_HEIGHT:60,
  CUSTOM_SECTION_TITLE_HEIGHT:50,
  COMMON_CUSTOM_TAB_CONTENT_PADDING:12,
};

// Common Layout Constants for consistent spacing across all screens
export const LayoutConstants = {
  // Standard content width used by CommonCardStyle and other components
  CONTENT_WIDTH: '95%' as const,
  // Horizontal gap on each side (calculated from 95% width)
  HORIZONTAL_GAP_PERCENTAGE: '2.5%' as const,
  // Standard horizontal padding for screen containers
  SCREEN_HORIZONTAL_PADDING: 12,
  // Standard bottom padding for scrollable content (space for fixed bottom elements)
  SCROLL_BOTTOM_PADDING: 80,
  // Standard vertical spacing between components
  COMPONENT_VERTICAL_SPACING: 4,
} as const;
export const TitleSectionStyleParams = {
  CommonWidth: '100%',
};

// Centralized Card Styling Constants
export const CardStyleConstants = {
  // Card Header/Title Styling
  HEADER_TEXT_COLOR: '#fff',
  HEADER_TEXT_SIZE: Sizes.LARGE,
  HEADER_TEXT_SIZE_LARGE: Sizes.XLARGE,
  HEADER_FONT_FAMILY: Fonts.ROBO_REGULAR,
  HEADER_FONT_WEIGHT: 'bold' as const,

  // Card Body Content Spacing
  CONTENT_VERTICAL_SPACING: 6,
  CONTENT_HORIZONTAL_PADDING: CommonUIParams.CUSTOM_PADDING_16,
  CONTENT_TOP_PADDING: CommonUIParams.CUSTOM_PADDING_14,

  // Sub-section spacing inside cards
  SUB_SECTION_SPACING: 10,
  SUB_SECTION_LABEL_SPACING: 4,

  // Common card dimensions
  CARD_BORDER_RADIUS: 2,
  CARD_ELEVATION: 2,
  CARD_MARGIN_VERTICAL: 4,
  CARD_WIDTH: '95%',
} as const;
