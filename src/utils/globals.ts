export const GC_CUSTOMER_ID = 'customer-id';
export const GC_MECHANIC_ID = 'customer-mechanic-id';
export const GC_KEEP_ME_LOGGED_IN = 'customer-keep-me-logged-in';
export const GC_SOCIAL_LOGGED_IN = 'customer-social-logged-in';
export const GC_TERMS_CONDITION_AGREE = 'customer-terms-agree';
export const GC_WORKREQUEST_PARAMS = 'customer-work-request-params';
export const GC_SETTINGS_INAPPLICATION = 'customer-in-application-notification';
export const GC_SETTINGS_INTEXT = 'customer-in-text';
export const GC_SETTINGS_INMAIL = 'customer-in-mail';
export const GC_SETTINGS_USELOCATION = 'customer-can-use-location';
export const GC_AVAILABILITY = 'customer-mechanic-availability';
export const GC_FCM_TOKEN = 'customer-fcm-token';
export const GC_BRANCH_RESPONSE_DATA = 'branch-response-data';
export const GC_FETCH_LOCATION = 'fetch-location-of-device';
export const NOTIFICATION_TYPE = [
  'workrequest',
  'appointment',
  'session',
  'invoice',
  'chat',
  'payment',
];
export const GC_MILES = 50;
export const GC_NEAR_MILES = 10;

export const GC_SIGNIN_PROVIDER_NORMAL = 'normal';
export const GC_SIGNIN_PROVIDER_GOOGLE = 'google';
export const GC_SIGNIN_PROVIDER_FACEBOOK = 'facebook';
export const GC_SIGNIN_PROVIDER_APPLE = 'apple.com';

export const GC_PROVIDER_APPLE = 'apple.com';

export const GC_APPLE_JWT_TOKEN_PREF = 'apple_jwt_token';
export const GC_APPLE_JWT_AUTHORIZATION_CODE = 'authorizationCode';
export const GC_SIGNUP_PROVIDER_TYPE_PREF = 'provider-type';
export const GC_SIGNUP_PROVIDER_TYPE_ID_PREF = 'provider-type-id';

export const GC_APPLE_GET_TOKEN_URL =
  'https://us-central1-mycanx-cf697.cloudfunctions.net/getRefreshToken';
export const GC_APPLE_REVOKE_TOKEN_URL =
  'https://us-central1-mycanx-cf697.cloudfunctions.net/revokeToken';

export const GC_APP_ID = 'com.mycanx.customer';
