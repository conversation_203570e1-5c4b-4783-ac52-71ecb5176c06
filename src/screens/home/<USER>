import React, {useEffect, useState, useMemo} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ImageBackground,
  TouchableOpacity,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import type {RootStackParamList} from '../../utils/configs/types';
import {AppCommonIcons, RouteNames} from '../../utils/constants/AppStrings';
import {Colors} from '../../utils/constants/Theme';
import {wp} from '../../utils/ResponsiveParams';
import MechanicCard from '../../components/cardstyles/MechanicCard';
import {VehicleService} from '../../utils/services/VehicleService';
import { get } from '@react-native-firebase/database';
import {useAuth} from '../../utils/configs/AuthContext';

interface ServiceItem {
  key: string;
  name: string;
  tools: Record<string, string>;
  subservice?: string[];
}

const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
): number => {
  const R = 6371;
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lon2 - lon1) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

const DashBoard: React.FC = () => {
  const {user} = useAuth();
  const customerId = user?.uid;
  const [enquiryTab, setEnquiryTab] = useState<
    {key: string; count: number; label: string}[]
  >([]);
  const [serviceList, setServiceList] = useState<ServiceItem[]>([]);
  const [mechanicList, setMechanicList] = useState<any>({});
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [customerLocation, setCustomerLocation] = useState<{
    lat: number;
    lng: number;
  } | null>(null);
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const filteredMechanics = useMemo(() => {
    let filtered = selectedService
      ? Object.keys(mechanicList)
          .filter(
            key =>
              mechanicList[key].services &&
              mechanicList[key].services[selectedService],
          )
          .map(key => ({id: key, ...mechanicList[key]}))
      : Object.keys(mechanicList).map(key => ({id: key, ...mechanicList[key]}));
    if (customerLocation) {
      filtered = filtered.sort((a, b) => {
        const distA = a.location
          ? calculateDistance(
              customerLocation.lat,
              customerLocation.lng,
              a.location.latitude,
              a.location.longitude,
            )
          : Number.MAX_VALUE;
        const distB = b.location
          ? calculateDistance(
              customerLocation.lat,
              customerLocation.lng,
              b.location.latitude,
              b.location.longitude,
            )
          : Number.MAX_VALUE;
        return distA - distB;
      });
    }
    return filtered;
  }, [mechanicList, selectedService, customerLocation]);

  useEffect(() => {
    if (!customerId) {
      return;
    }

    const fetchData = async () => {
      try {
        const [
          servicesSnapshot,
          pendingSnapshot,
          multipleSnapshot,
          appointmentsSnapshot,
          mechanicsSnapshot,
        ] = await Promise.all([
          get(VehicleService.getVehicleServices()),
          get(VehicleService.getPendingRequestData(customerId)),
          get(VehicleService.getMultiplePendingRequest(customerId)),
          get(VehicleService.getAppointments(customerId)),
          get(VehicleService.getLoggedMechanics()),
        ]);

        const services = servicesSnapshot.val() || {};
        const serviceArray = Object.keys(services).map(key => ({
          key,
          ...services[key],
        })).reverse() as ServiceItem[];
        setServiceList(serviceArray);
        if (serviceArray.length > 0) {
          setSelectedService(serviceArray[0].key);
        }

        const pendingRequests = pendingSnapshot.val() || {};
        const pendingCount = Object.keys(pendingRequests).length;

        const multipleRequests = multipleSnapshot.val() || {};
        const multipleCount = Object.keys(multipleRequests).length;

        const appointments = appointmentsSnapshot.val() || {};
        const appointmentsCount = Object.keys(appointments).length;
        setEnquiryTab([
          {
            key: 'appointments',
            count: appointmentsCount,
            label: 'Upcoming Appointments',
          },
          {
            key: 'pendingRequests',
            count: pendingCount + multipleCount,
            label: 'Pending Requests',
          },
        ]);
        const mechanics = mechanicsSnapshot.val() || {};
        setMechanicList(mechanics);
      } catch (error) {
        console.error('Error fetching data:', error);
        setServiceList([]);
        setEnquiryTab([]);
        setMechanicList([]);
      }
    };
    fetchData();
    setCustomerLocation({lat: 0, lng: 0});
  }, [customerId]);

  const renderHeader = () => (
    <View style={styles.mainContentContainer}>
      <View style={styles.enquirySection}>
        {enquiryTab.map(item => (
          <TouchableOpacity
            key={item.key}
            style={styles.enquiryFeatureBox}
            activeOpacity={0.7}>
            <Text style={styles.enquiryCount}>{item.count}</Text>
            <View style={styles.enquiryTextContainer}>
              <Text style={styles.enquiryLabel}>{item.label}</Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.userFeatureSection}>
        {serviceList.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.userFeatureBox,
              selectedService === item.key && styles.selectedServiceBox,
            ]}
            activeOpacity={0.7}
            onPress={() => setSelectedService(item.key)}>
            {/* <Image source={item.icon} style={styles.featureIcon} /> */}
            <Text style={[
              styles.featureLabel,
              selectedService === item.key && styles.selectedFeatureLabel,
            ]} numberOfLines={2}>
              {item.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <ImageBackground
      style={styles.container}
      source={AppCommonIcons.MCX_BACKGROUND_IMAGE}
      resizeMode="cover">
      <FlatList
        data={filteredMechanics}
        keyExtractor={item => item.id.toString()}
        renderItem={({item, index}) => (
          <MechanicCard
            id={item.id}
            name={
              item.name ||
              `${item['first-name'] || ''} ${item['last-name'] || ''}`.trim()
            }
            address={
              item.address || [item.city, item.state].filter(Boolean).join(', ')
            }
            userRating={item.userRating || item['mechanic-rating'] || 0}
            ratingOutOf={5}
            availability={item.availability || ''}
            showFavoriteIcon={false}
            cardStyle={styles.mechanicCard}
            onCardPress={() => {
              const stackNavigation = navigation.getParent()?.getParent();
              stackNavigation?.navigate(
                RouteNames.MCX_NAV_MechanicProfilePage,
                {
                  mechanic: item,
                  mechanics: filteredMechanics,
                  currentIndex: index,
                },
              );
            }}
          />
        )}
        ListHeaderComponent={renderHeader}
        style={styles.mechanicList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
      />
    </ImageBackground>
  );
};
const isSmallDevice = wp(100) < 360;
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  mainContentContainer: {
    flex: 1,
    paddingTop: 10,
  },
  enquirySection: {
    flexDirection: 'row',
    backgroundColor: Colors.SECONDARY,
    borderRadius: 2,
    alignContent: 'center',
    padding: 6,
    marginBottom: 8,
  },
  enquiryFeatureBox: {
    flex: 1,
    backgroundColor: Colors.PRIMARY,
    borderRadius: 2,
    minHeight: isSmallDevice ? 55 : 65,
    borderWidth: 1,
    borderColor: Colors.COMMON_WHITE_SHADE,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: isSmallDevice ? 6 : 8,
    paddingVertical: isSmallDevice ? 6 : 8,
  },
  enquiryCount: {
    color: '#fff',
    fontSize: isSmallDevice ? wp(7) : wp(8),
    fontWeight: 'bold',
    minWidth: isSmallDevice ? wp(7) : wp(8),
    textAlign: 'center',
  },
  enquiryTextContainer: {
    flex: 1,
    marginLeft: isSmallDevice ? 6 : 8,
    justifyContent: 'center',
  },
  enquiryLabel: {
    color: '#fff',
    fontSize: isSmallDevice ? wp(2.8) : wp(3),
    fontWeight: '600',
    textAlign: 'left',
    flexWrap: 'wrap',
    lineHeight: isSmallDevice ? wp(3.5) : wp(4),
  },
  userFeatureSection: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: Colors.SECONDARY,
    borderRadius: 4,
    padding: 6,
    marginBottom: 12,
  },
  userFeatureBox: {
    width: '50%',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 2,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: isSmallDevice ? wp(20) : wp(22),
    borderWidth: 1,
    borderColor: Colors.SECONDARY,
    paddingVertical: isSmallDevice ? wp(2.5) : wp(3),
    paddingHorizontal: isSmallDevice ? wp(1.5) : wp(2),
  },
  featureIcon: {
    width: isSmallDevice ? wp(9) : wp(10),
    height: isSmallDevice ? wp(9) : wp(10),
    marginBottom: isSmallDevice ? wp(1.5) : wp(2),
    tintColor: '#fff',
    resizeMode: 'contain',
  },
  featureLabel: {
    color: '#fff',
    fontSize: isSmallDevice ? wp(2.8) : wp(3),
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: isSmallDevice ? wp(3.5) : wp(3.8),
  },
  selectedServiceBox: {
    backgroundColor: 'rgba(234, 14, 14, 0.8)',
    borderColor: '#ea0e0e80',
    borderWidth: 2,
  },
  selectedFeatureLabel: {
    color: '#fff',
    fontWeight: '700',
  },
  mechanicList: {
    marginTop: 2,
  },
  mechanicCard: {
    backgroundColor: '#fff',
    borderRadius: 2,
    padding: 16,
    marginBottom: 2,
  },
  flatListContent: {
    flexGrow: 1,
    paddingBottom: 20,
    paddingHorizontal: 12,
  },
});

export default DashBoard;
/* import React, {useEffect, useState, useMemo} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ImageBackground,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import type {RootStackParamList, MechanicItem} from '../../utils/configs/types';
import {AppCommonIcons, RouteNames} from '../../utils/constants/AppStrings';
import {Colors} from '../../utils/constants/Theme';
import {wp} from '../../utils/ResponsiveParams';
import MechanicCard from '../../components/cardstyles/MechanicCard';
import {VehicleService} from '../../utils/services/VehicleService';
import {useAuth} from '../../utils/configs/AuthContext';

interface ServiceItem {
  key: string;
  name: string;
  tools: Record<string, string>;
  subservice?: string[];
}

const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
): number => {
  const R = 6371;
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lon2 - lon1) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

const DashBoard: React.FC = () => {
  const {user} = useAuth();
  const customerId = user?.uid;
  const [enquiryTab, setEnquiryTab] = useState<
    {key: string; count: number; label: string}[]
  >([]);
  const [serviceList, setServiceList] = useState<ServiceItem[]>([]);
  const [mechanicList, setMechanicList] = useState<MechanicItem[]>([]);
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [customerLocation, setCustomerLocation] = useState<{
    lat: number;
    lng: number;
  } | null>(null);
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const filteredMechanics = useMemo(() => {
    let filtered = selectedService
      ? mechanicList.filter(
          mech => mech.services && mech.services[selectedService],
        )
      : mechanicList;
    if (customerLocation) {
      filtered = filtered.sort((a, b) => {
        const distA = a.location
          ? calculateDistance(
              customerLocation.lat,
              customerLocation.lng,
              a.location.latitude,
              a.location.longitude,
            )
          : Number.MAX_VALUE;
        const distB = b.location
          ? calculateDistance(
              customerLocation.lat,
              customerLocation.lng,
              b.location.latitude,
              b.location.longitude,
            )
          : Number.MAX_VALUE;
        return distA - distB;
      });
    }
    return filtered;
  }, [mechanicList, selectedService, customerLocation]);

  useEffect(() => {
    if (!customerId) {
      return;
    }

    const fetchData = async () => {
      try {
        const [
          servicesSnapshot,
          pendingSnapshot,
          multipleSnapshot,
          appointmentsSnapshot,
          mechanicsSnapshot,
        ] = await Promise.all([
          get(VehicleService.getVehicleServices()),
          get(VehicleService.getPendingRequestData(customerId)),
          get(VehicleService.getMultiplePendingRequest(customerId)),
          get(VehicleService.getAppointments(customerId)),
          get(VehicleService.getLoggedMechanics()),
        ]);

        const services = servicesSnapshot.val() || {};
        const serviceArray = Object.keys(services).map(key => ({
          key,
          ...services[key],
        })) as ServiceItem[];
        setServiceList(serviceArray);

        const pendingRequests = pendingSnapshot.val() || {};
        const pendingCount = Object.keys(pendingRequests).length;

        const multipleRequests = multipleSnapshot.val() || {};
        const multipleCount = Object.keys(multipleRequests).length;

        const appointments = appointmentsSnapshot.val() || {};
        const appointmentsCount = Object.keys(appointments).length;
        setEnquiryTab([
          {
            key: 'appointments',
            count: appointmentsCount,
            label: 'Upcoming Appointments',
          },
          {
            key: 'pendingRequests',
            count: pendingCount + multipleCount,
            label: 'Pending Requests',
          },
        ]);
        const mechanics = mechanicsSnapshot.val() || {};
        const mechanicArray: MechanicItem[] = Object.keys(mechanics).map(
          (key, index) => {
            const mech = mechanics[key];
            const firstName = mech['first-name'] || '';
            const lastName = mech['last-name'] || '';
            const city = mech.city || '';
            const state = mech.state || '';
            return {
              id: index + 1,
              name: `${firstName} ${lastName}`.trim() || '',
              address: [city, state].filter(Boolean).join(', ') || '',
              userRating: mech['mechanic-rating'] || 0,
              ratingOutOf: 5,
              availability: mech.availability ?? '',
              favourite: false,
              services: mech.services || {},
              location: mech.location,
            };
          },
        );
        setMechanicList(mechanicArray);
      } catch (error) {
        console.error('Error fetching data:', error);
        setServiceList([]);
        setEnquiryTab([]);
        setMechanicList([]);
      }
    };
    fetchData();
    setCustomerLocation({lat: 0, lng: 0});
  }, [customerId]);

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.welcomeSection}>
        <Text style={styles.welcomeText}>Welcome Back</Text>
        <Text style={styles.welcomeSubtext}>
          Find trusted mechanics near you
        </Text>
      </View>

      <View style={styles.statsContainer}>
        {enquiryTab.map((item, index) => (
          <TouchableOpacity
            key={item.key}
            style={[
              styles.statCard,
              index === 0 ? styles.statCardFirst : styles.statCardSecond,
            ]}
            activeOpacity={0.8}>
            <View style={styles.statContent}>
              <Text style={styles.statNumber}>{item.count}</Text>
              <Text style={styles.statLabel}>{item.label}</Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.servicesSection}>
        <Text style={styles.sectionTitle}>Services</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.servicesScrollContent}>
          {serviceList.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.serviceCard,
                selectedService === item.key && styles.serviceCardSelected,
              ]}
              activeOpacity={0.8}
              onPress={() =>
                setSelectedService(
                  selectedService === item.key ? null : item.key,
                )
              }>
              <View style={styles.serviceIconPlaceholder}>
                <Text style={styles.serviceIcon}>🔧</Text>
              </View>
              <Text
                style={[
                  styles.serviceLabel,
                  selectedService === item.key && styles.serviceLabelSelected,
                ]}
                numberOfLines={2}>
                {item.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.mechanicsHeader}>
        <Text style={styles.sectionTitle}>
          {selectedService ? 'Filtered Mechanics' : 'Available Mechanics'}
        </Text>
        {selectedService && (
          <TouchableOpacity
            onPress={() => setSelectedService(null)}
            style={styles.clearFilterButton}>
            <Text style={styles.clearFilterText}>Clear Filter</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={filteredMechanics}
        keyExtractor={(item: MechanicItem) => item.id.toString()}
        renderItem={({item, index}: {item: MechanicItem; index: number}) => (
          <View style={styles.mechanicCardWrapper}>
            <MechanicCard
              id={item.id}
              name={item.name}
              address={item.address}
              userRating={item.userRating}
              ratingOutOf={item.ratingOutOf}
              availability={item.availability}
              showFavoriteIcon={false}
              cardStyle={styles.mechanicCard}
              onCardPress={() => {
                const stackNavigation = navigation.getParent()?.getParent();
                stackNavigation?.navigate(
                  RouteNames.MCX_NAV_MechanicProfilePage,
                  {
                    mechanic: item,
                    mechanics: filteredMechanics,
                    currentIndex: index,
                  },
                );
              }}
            />
          </View>
        )}
        ListHeaderComponent={renderHeader}
        style={styles.flatList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
      />
    </View>
  );
};

const isSmallDevice = wp(100) < 360;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e', // Dark background similar to login
  },
  headerContainer: {
    paddingHorizontal: wp(4),
    paddingTop: wp(4),
  },
  welcomeSection: {
    marginBottom: wp(6),
    paddingTop: wp(2),
  },
  welcomeText: {
    fontSize: isSmallDevice ? wp(7) : wp(8),
    fontWeight: '700',
    color: '#ffffff',
    marginBottom: wp(1),
  },
  welcomeSubtext: {
    fontSize: isSmallDevice ? wp(3.5) : wp(4),
    color: '#a0a0b8',
    fontWeight: '400',
  },
  statsContainer: {
    flexDirection: 'row',
    gap: wp(3),
    marginBottom: wp(6),
  },
  statCard: {
    flex: 1,
    borderRadius: wp(4),
    padding: wp(4),
    minHeight: wp(20),
    justifyContent: 'center',
    // Glassmorphism effect
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    // Shadow for depth
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  statCardFirst: {
    // Gradient background for first card (similar to login button)
    backgroundColor: 'rgba(255, 107, 107, 0.8)', // Coral gradient
  },
  statCardSecond: {
    // Gradient background for second card
    backgroundColor: 'rgba(74, 144, 226, 0.8)', // Blue gradient
  },
  statContent: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: isSmallDevice ? wp(8) : wp(10),
    fontWeight: '800',
    color: '#ffffff',
    marginBottom: wp(1),
  },
  statLabel: {
    fontSize: isSmallDevice ? wp(2.8) : wp(3.2),
    color: '#ffffff',
    textAlign: 'center',
    fontWeight: '600',
    lineHeight: isSmallDevice ? wp(3.5) : wp(4),
  },
  servicesSection: {
    marginBottom: wp(6),
  },
  sectionTitle: {
    fontSize: isSmallDevice ? wp(5) : wp(5.5),
    fontWeight: '700',
    color: '#ffffff',
    marginBottom: wp(4),
  },
  servicesScrollContent: {
    paddingRight: wp(4),
  },
  serviceCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: wp(3),
    padding: wp(3),
    marginRight: wp(3),
    alignItems: 'center',
    minWidth: wp(20),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    // Subtle shadow
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  serviceCardSelected: {
    backgroundColor: 'rgba(255, 107, 107, 0.3)',
    borderColor: 'rgba(255, 107, 107, 0.6)',
    transform: [{scale: 1.05}],
  },
  serviceIconPlaceholder: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: wp(2),
  },
  serviceIcon: {
    fontSize: wp(6),
  },
  serviceLabel: {
    fontSize: isSmallDevice ? wp(2.5) : wp(3),
    color: '#ffffff',
    textAlign: 'center',
    fontWeight: '600',
    lineHeight: isSmallDevice ? wp(3) : wp(3.5),
  },
  serviceLabelSelected: {
    color: '#ffffff',
    fontWeight: '700',
  },
  mechanicsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: wp(4),
  },
  clearFilterButton: {
    backgroundColor: 'rgba(255, 107, 107, 0.8)',
    paddingHorizontal: wp(3),
    paddingVertical: wp(1.5),
    borderRadius: wp(2),
  },
  clearFilterText: {
    color: '#ffffff',
    fontSize: wp(3),
    fontWeight: '600',
  },
  mechanicCardWrapper: {
    marginHorizontal: wp(4),
    marginBottom: wp(3),
  },
  mechanicCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: wp(4),
    padding: wp(4),
    // Modern card shadow
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  flatList: {
    flex: 1,
  },
  flatListContent: {
    flexGrow: 1,
    paddingBottom: wp(8),
  },
});

export default DashBoard;
 */
