import React, {useEffect, useState} from 'react';
import {VehicleService} from '../../../utils/services/VehicleService';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import {RouteProp, useRoute, useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Colors} from '../../../utils/constants/Theme';
import {AppCommonIcons, AppStrings} from '../../../utils/constants/AppStrings';
import type {
  MechanicItem,
  RootStackParamList,
} from '../../../utils/configs/types';

type ParamList = {
  MechanicProfilePage: {
    mechanic: MechanicItem;
    mechanics?: MechanicItem[];
    currentIndex?: number;
  };
};

const MechanicProfilePage: React.FC = () => {
  const route = useRoute<RouteProp<ParamList, 'MechanicProfilePage'>>();
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const {mechanic, mechanics, currentIndex} = route.params;

  const getFullName = () => {
    if (mechanic['first-name'] && mechanic['last-name']) {
      return `${mechanic['first-name']} ${mechanic['last-name']}`;
    }
    return mechanic.name || 'Unknown Mechanic';
  };

  const getLocationString = () => {
    const parts = [];
    if (mechanic.address1) parts.push(mechanic.address1);
    if (mechanic.address2) parts.push(mechanic.address2);
    if (mechanic.city) parts.push(mechanic.city);
    if (mechanic.state) parts.push(mechanic.state);
    if (mechanic.country) parts.push(mechanic.country);
    if (mechanic.zipcode) parts.push(mechanic.zipcode);

    if (parts.length > 0) {
      return parts.join(', ');
    }

    if (mechanic.locationDetails && mechanic.locationDetails.length > 0) {
      return mechanic.locationDetails.join(', ');
    }

    return 'Location not available';
  };

  const getCancellationRate = () => {
    const cancelledTotal = mechanic.counts?.['cancelled-total'] ?? 0;
    const appointmentCount = mechanic.counts?.['appointment-count'] ?? 0;

    if (appointmentCount === 0) return '0%';

    const rate = (cancelledTotal / appointmentCount) * 100;
    return `${Math.round(rate)}%`;
  };

  const [serviceIdNameMap, setServiceIdNameMap] = useState<
    Record<string, string>
  >({});

  useEffect(() => {
    const fetchServiceMap = async () => {
      const map = await VehicleService.getServiceIdNameMap();
      setServiceIdNameMap(map);
    };
    fetchServiceMap();
  }, []);

  const goToMechanic = (index: number) => {
    if (!mechanics || index < 0 || index >= mechanics.length) {
      return;
    }
    navigation.navigate('MechanicProfilePage', {
      mechanic: mechanics[index],
      mechanics,
      currentIndex: index,
    });
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a2332" />

      {/* Fixed Header */}
      <SafeAreaView style={styles.headerSafeArea}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}>
            <Image
              source={AppCommonIcons.MCX_ARROW_RIGHT}
              style={styles.backIcon}
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{AppStrings.MCX_MECHANIC_DETAIL_TITLE}</Text>
          <View style={styles.headerSpacer} />
        </View>
      </SafeAreaView>

      {/* Scrollable Content */}
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>MY MECHANIC</Text>
          <View style={styles.mechanicCard}>
            <View style={styles.mechanicHeader}>
              <Image
                source={
                  mechanic.imageUrl
                    ? {uri: mechanic.imageUrl}
                    : AppCommonIcons.MCX_USER_PROFILE_PIC
                }
                style={styles.mechanicImage}
              />
              <View style={styles.mechanicInfo}>
                <Text style={styles.mechanicName}>{getFullName()}</Text>
                <View style={styles.ratingContainer}>
                  {mechanic['mechanic-rating'] && (
                    <>
                      <Text style={styles.ratingText}>
                        {mechanic['mechanic-rating']}/5
                      </Text>
                      <Text style={styles.starIcon}>⭐</Text>
                    </>
                  )}
                </View>
              </View>
            </View>

            <View style={styles.infoGrid}>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>{AppStrings.MCX_MECHANIC_EXPERIENCE_LABEL}</Text>
                <Text style={styles.infoValue}>
                  {mechanic.experience || 'N/A'}
                </Text>
              </View>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>{AppStrings.MCX_MECHANIC_CERTIFICATIONS_LABEL}</Text>
                <Text style={styles.infoValue}>
                  {mechanic.certificatesCount ?? mechanic.certifications ?? 0}
                </Text>
              </View>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>{AppStrings.MCX_MECHANIC_AVAILABILITY_TITLE}</Text>
                <Text
                  style={[
                    styles.infoValue,
                    {color: mechanic.availability ? '#4CAF50' : '#f44336'},
                  ]}>
                  {mechanic.availability ? 'Available' : 'Not Available'}
                </Text>
              </View>
            </View>

            <View style={styles.locationBox}>
              <Text style={styles.locationTitle}>{AppStrings.MCX_MECHANIC_LOCATION_TITLE}</Text>
              <Text style={styles.locationText}>{getLocationString()}</Text>
            </View>
          </View>
        </View>

        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>{AppStrings.MCX_MECHANIC_PERFORMANCE_METRICS_TITLE}</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statBox}>
              <Text style={styles.statValue}>{getCancellationRate()}</Text>
              <Text style={styles.statLabel}>{AppStrings.MCX_MECHANIC_CANCELLATION_RATE_LABEL}</Text>
            </View>
            <View style={styles.statBox}>
              <Text style={styles.statValue}>
                {mechanic.counts?.['rightnow-count'] ??
                  mechanic.byAvailability ??
                  0}
              </Text>
              <Text style={styles.statLabel}>{AppStrings.MCX_MECHANIC_BY_AVAILABILITY_LABEL}</Text>
            </View>
            <View style={styles.statBox}>
              <Text style={styles.statValue}>
                {mechanic.counts?.['appointment-count'] ??
                  mechanic.byAppointment ??
                  0}
              </Text>
              <Text style={styles.statLabel}>{AppStrings.MCX_MECHANIC_TOTAL_APPOINTMENTS_LABEL}</Text>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{AppStrings.MCX_MECHANIC_SERVICES_TITLE}</Text>
          <View style={styles.servicesContainer}>
            {mechanic.services && Object.keys(mechanic.services).length > 0 ? (
              Object.keys(mechanic.services).map((key, idx) => (
                <View key={idx} style={styles.serviceChip}>
                  <Text style={styles.serviceText}>
                    {serviceIdNameMap[key] ||
                      mechanic.services![key]['service-type'] ||
                      key}
                  </Text>
                </View>
              ))
            ) : (
              <View style={styles.noDataContainer}>
                <Text style={styles.noDataIcon}>🔧</Text>
                <Text style={styles.noDataText}>{AppStrings.MCX_MECHANIC_NO_SERVICES_TEXT}</Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{AppStrings.MCX_MECHANIC_PERSONAL_INFO_TITLE}</Text>
          <View style={styles.infoCard}>
            {mechanic.gender && (
              <View style={styles.infoRow}>
                <Text style={styles.infoRowLabel}>{AppStrings.MCX_MECHANIC_GENDER_LABEL}</Text>
                <Text style={styles.infoRowValue}>{mechanic.gender}</Text>
              </View>
            )}
            {mechanic.dateofbirth && (
              <View style={styles.infoRow}>
                <Text style={styles.infoRowLabel}>{AppStrings.MCX_MECHANIC_DOB_LABEL}</Text>
                <Text style={styles.infoRowValue}>{mechanic.dateofbirth}</Text>
              </View>
            )}
            {mechanic.email && (
              <View style={styles.infoRow}>
                <Text style={styles.infoRowLabel}>{AppStrings.MCX_MECHANIC_EMAIL_LABEL}</Text>
                <Text style={styles.infoRowValue}>{mechanic.email}</Text>
              </View>
            )}
            {mechanic.mobile && (
              <View style={styles.infoRow}>
                <Text style={styles.infoRowLabel}>{AppStrings.MCX_MECHANIC_MOBILE_LABEL}</Text>
                <Text style={styles.infoRowValue}>{mechanic.mobile}</Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{AppStrings.MCX_MECHANIC_PROFESSIONAL_DETAILS_TITLE}</Text>
          <View style={styles.infoCard}>
            {mechanic['provider-type'] && (
              <View style={styles.infoRow}>
                <Text style={styles.infoRowLabel}>{AppStrings.MCX_MECHANIC_PROVIDER_TYPE_LABEL}</Text>
                <Text style={styles.infoRowValue}>
                  {mechanic['provider-type']}
                </Text>
              </View>
            )}
            {mechanic['registration-status'] && (
              <View style={styles.infoRow}>
                <Text style={styles.infoRowLabel}>{AppStrings.MCX_MECHANIC_REGISTRATION_STATUS_LABEL}</Text>
                <Text style={styles.infoRowValue}>
                  {mechanic['registration-status']}
                </Text>
              </View>
            )}
            {mechanic['login-status'] !== undefined && (
              <View style={styles.infoRow}>
                <Text style={styles.infoRowLabel}>{AppStrings.MCX_MECHANIC_STATUS_LABEL}</Text>
                <Text
                  style={[
                    styles.infoRowValue,
                    {color: mechanic['login-status'] ? '#4CAF50' : '#f44336'},
                  ]}>
                  {mechanic['login-status'] ? 'Online' : 'Offline'}
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>REVIEWS</Text>
          <View style={styles.reviewsContainer}>
            <Image
              source={AppCommonIcons.MCX_EXCEPTION_NO_DATA_FOUND_ICON}
              style={styles.noDataImage}
            />
            <Text style={styles.noDataText}>SORRY NO DATA FOUND</Text>
          </View>
        </View>
      </ScrollView>

      {/* Fixed Footer */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.bookButton}
          onPress={() =>
            navigation.navigate('BookAppointmentScreen', {mechanic})
          }>
          <Text style={styles.bookButtonText}>BOOK APPOINTMENT</Text>
        </TouchableOpacity>

        {mechanics && mechanics.length > 1 && (
          <View style={styles.navigationButtons}>
            <TouchableOpacity
              style={[
                styles.navButton,
                (!mechanics || currentIndex === 0) && styles.navButtonDisabled,
              ]}
              disabled={!mechanics || currentIndex === 0}
              onPress={() => goToMechanic((currentIndex ?? 0) - 1)}>
              <Text style={styles.navButtonText}>PREVIOUS</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.navButton,
                (!mechanics || currentIndex === mechanics.length - 1) &&
                  styles.navButtonDisabled,
              ]}
              disabled={!mechanics || currentIndex === mechanics.length - 1}
              onPress={() => goToMechanic((currentIndex ?? 0) + 1)}>
              <Text style={styles.navButtonText}>NEXT</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BACKGROUND,
  },

  // Header Styles
  headerSafeArea: {
    backgroundColor: '#1a2332',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1f2a3a',
    paddingVertical: 16,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.COMMON_WHITE_SHADE,
    transform: [{rotate: '180deg'}],
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: Colors.COMMON_WHITE_SHADE,
    fontSize: 18,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  headerSpacer: {
    width: 40,
  },

  // Scroll Container
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 20,
  },

  // Section Styles
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    color: Colors.PRIMARY_DARK,
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 12,
    letterSpacing: 0.5,
  },

  // Mechanic Card Styles
  mechanicCard: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 12,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  mechanicHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  mechanicImage: {
    width: 70,
    height: 70,
    borderRadius: 35,
    marginRight: 16,
    borderWidth: 2,
    borderColor: '#e0e0e0',
  },
  mechanicInfo: {
    flex: 1,
  },
  mechanicName: {
    fontWeight: 'bold',
    fontSize: 20,
    color: Colors.TEXT_COLOR,
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    color: Colors.PRIMARY_DARK,
    fontWeight: '600',
    marginRight: 4,
  },
  starIcon: {
    fontSize: 16,
  },

  // Info Grid
  infoGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingVertical: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  infoItem: {
    flex: 1,
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 12,
    color: Colors.PRIMARY_DARK,
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'center',
  },
  infoValue: {
    fontWeight: 'bold',
    fontSize: 14,
    color: Colors.TEXT_COLOR,
    textAlign: 'center',
  },

  // Location Box
  locationBox: {
    backgroundColor: '#f0f4f8',
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: Colors.PRIMARY,
  },
  locationTitle: {
    fontSize: 12,
    color: Colors.PRIMARY_DARK,
    fontWeight: '600',
    marginBottom: 8,
  },
  locationText: {
    fontSize: 14,
    color: Colors.TEXT_COLOR,
    lineHeight: 20,
  },

  // Stats Section
  statsSection: {
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 12,
    padding: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statBox: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 8,
  },
  statValue: {
    fontWeight: 'bold',
    fontSize: 20,
    color: Colors.PRIMARY,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 11,
    color: Colors.PRIMARY_DARK,
    textAlign: 'center',
    fontWeight: '500',
  },

  // Services
  servicesContainer: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 12,
    padding: 16,
  },
  serviceChip: {
    backgroundColor: Colors.PRIMARY + '15',
    borderColor: Colors.PRIMARY + '30',
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  serviceText: {
    fontSize: 12,
    color: Colors.PRIMARY,
    fontWeight: '600',
  },

  // Info Card
  infoCard: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 12,
    padding: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoRowLabel: {
    fontSize: 14,
    color: Colors.PRIMARY_DARK,
    fontWeight: '600',
    flex: 1,
  },
  infoRowValue: {
    fontSize: 14,
    color: Colors.TEXT_COLOR,
    fontWeight: '500',
    flex: 1.5,
    textAlign: 'right',
  },

  // Reviews
  reviewsContainer: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 12,
    padding: 32,
    alignItems: 'center',
  },
  noDataContainer: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  noDataIcon: {
    fontSize: 48,
    marginBottom: 12,
  },
  noDataImage: {
    width: 80,
    height: 80,
    marginBottom: 12,
    opacity: 0.5,
  },
  noDataText: {
    fontSize: 14,
    color: Colors.PRIMARY_DARK,
    fontWeight: '500',
  },

  // Footer Styles
  footer: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: -2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  bookButton: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.PRIMARY,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  bookButtonText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
    letterSpacing: 0.5,
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  navButton: {
    flex: 1,
    backgroundColor: Colors.SECONDARY,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  navButtonDisabled: {
    opacity: 0.5,
    backgroundColor: '#cccccc',
  },
  navButtonText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 14,
  },
});

export default MechanicProfilePage;
