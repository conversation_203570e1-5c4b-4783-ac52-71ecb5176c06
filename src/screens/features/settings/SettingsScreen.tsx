// SettingsScreen.tsx
import React, { useState } from 'react';
import { View, Text, StyleSheet, Switch } from 'react-native';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import CommonCardStyle from '../../../components/common/CommonCardStyle';
import HorizontalDivider from '../../../components/common/HorizontalDivider';
import { Colors, Fonts, Sizes } from '../../../utils/constants/Theme';
import { AppStrings } from '../../../utils/constants/AppStrings';
import { notificationSettings } from '../../../utils/templates/TemplateConfig';

const SettingsScreen = () => {
  const [settings, setSettings] = useState({
    inApplication: true,
    textNotification: false,
    emailNotification: false,
  });

  const handleToggle = (setting: string, value: boolean) => {
    setSettings((prevSettings) => ({ ...prevSettings, [setting]: value }));
  };

  return (
    <ScreenLayout
      useScrollView={true}
      useImageBackground={true}
      centerContent={false}
      topPadding={18}>
        <View style={styles.container}>
        <CommonCardStyle
          header={AppStrings.MCX_GPS_SETTINGS_TEXT}
          headerColor={Colors.SECONDARY}
          textColor={Colors.COMMON_WHITE_SHADE}
          isCardContainerDecorated={false}
          isTitleBordered={true}
          cardBackgroundColor="transparent"
          style={styles.cardContent}
          marginVertical={8}
        >
          <View style={styles.row}>
            <Text style={styles.label}>{AppStrings.MCX_USE_MY_LOCATION_TITLE}</Text>
            <Switch
              value={settings.useMyLocation}
              onValueChange={(value) => handleToggle('useMyLocation', value)}
              trackColor={{ false: '#ccc', true: Colors.PRIMARY }}
              thumbColor={settings.useMyLocation ? Colors.PRIMARY : '#f4f3f4'}
            />
          </View>
        </CommonCardStyle>
        <CommonCardStyle
          header={AppStrings.MCX_NOTIFICATION_TITLE}
          headerColor={Colors.SECONDARY}
          textColor={Colors.COMMON_WHITE_SHADE}
          isCardContainerDecorated={false}
          isTitleBordered={true}
          cardBackgroundColor="transparent"
          style={styles.cardContent}
        >

          {notificationSettings.map((setting, index) => (
            <React.Fragment key={index}>
              <View style={styles.row}>
                <Text style={styles.label}>{setting.label}</Text>
                <Switch
                  value={settings[setting.value]}
                  onValueChange={(value) => handleToggle(setting.value, value)}
                  trackColor={{ false: '#ccc', true: Colors.PRIMARY }}
                  thumbColor={settings[setting.value] ? Colors.PRIMARY : '#f4f3f4'}
                />
              </View>
              <HorizontalDivider isFullWidth={true} style={styles.divider} />
            </React.Fragment>
          ))}

        </CommonCardStyle>
      </View>
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  cardContent: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    margin: 8,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 14,
  },
  label: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    opacity: 0.8,
    fontWeight: '600',
    paddingHorizontal: 12,
  },
  divider: {
    marginLeft: 0,
    marginRight: 0
  }
});

export default SettingsScreen;