import React, { useCallback, useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';

import EmptyMessages from './EmptyMessages';
import MessageSection from './MessageSection';
import { messageSection, messageTabData } from '../../../../utils/templates/TemplateConfig';
import CustomTab from '../../../../components/common/CustomTabs';
import ScreenLayout from '../../../../components/layout/ScreenLayout';
import TitleSection from '../../../../components/common/TitleSection';
import { AppStrings } from '../../../../utils/constants/AppStrings';
import { Colors } from '../../../../utils/constants/Theme';
import PendingScreen from './PendingScreen';

const MessagesScreen = () => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [messages, setMessages] = useState([]);

  // Reset tab to ALL when screen is focused
  useFocusEffect(
    useCallback(() => {
      setActiveTabIndex(0);
    }, [])
  );

  const handleTabPress = (index: number) => {
    setActiveTabIndex(index);
  };

  const handleDeleteMessage = (messageId: string) => {
    // Logic to delete a message
    console.log('Delete message:', messageId);
  };

  const renderTabs = () => (
    <View style={styles.tabsContainer}>
      {Object.entries(messageTabData).map(([key, label], idx) => (
        <CustomTab
          key={key}
          label={label}
          active={activeTabIndex === idx}
          onPress={() => handleTabPress(idx)}
        />
      ))}
    </View>
  );

  const renderTabContent = () => {
    switch (activeTabIndex) {
      case 0: // ALL
        return <EmptyMessages />;
      case 1: // SERVICES
      case 2: // SYSTEMS
        return messages.length > 0 ? (
          <>
            <MessageSection
              sections={[messageSection.TYPES, messageSection.DETAILS]}
              onDeletePress={() => handleDeleteMessage}
            />
            {messages.map((_, index) => (
              <View key={index}>
                {/* Message items would go here */}
              </View>
            ))}
          </>
        ) : (
          <EmptyMessages />
        );
      case 3:
        return <PendingScreen />;
      // No need for PENDING, handled by navigation
      default:
        return <EmptyMessages />;
    }
  };
  return (
    <ScreenLayout
      useScrollView={false}
      useImageBackground={true}
      centerContent={false}
    >
      {/* Title Section */}
      <TitleSection
        title={AppStrings.MCX_MESSAGES_TITLE}
        bgColor={Colors.PRIMARY}
        textColor="#fff"
        style={styles.titleSection}
      />
      <View style={styles.container}>
        {renderTabs()}
        <View style={styles.contentContainer}>
          <ScrollView>
            {renderTabContent()}
          </ScrollView>
        </View>
      </View>
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.COMMON_TAB_SECTION_BG_COLOR,
    justifyContent: 'space-around',
    marginHorizontal: 12,
    marginTop: 20,
  },
  contentContainer: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    paddingBottom: 20,
  },
});

export default MessagesScreen;