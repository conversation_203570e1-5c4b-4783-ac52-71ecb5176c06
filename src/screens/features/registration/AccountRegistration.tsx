import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import TitleSection from '../../../components/common/TitleSection';
import CustomTab from '../../../components/common/CustomTabs';
import AppBar from '../../../components/common/AppBar';
import {registrationTabData} from '../../../utils/templates/TemplateConfig';
import {AppStrings} from '../../../utils/constants/AppStrings';
import {Colors} from '../../../utils/constants/Theme';
import RegistrationPersonalDetails from './components/RegistrationPersonalDetails';
import RegistrationCreatePassword from './components/RegistrationCreatePassword';
import LoadVinScreen from './components/LoadVinScreen';
import AccountCreatedScreen from './components/AccountCreatedScreen';

type TabType = keyof typeof registrationTabData;

interface VehicleInfo {
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
  warning: string;
  error: string;
  valid: boolean;
}

const AccountRegistration = () => {
  const navigation = useNavigation();
  const handleTabChange = (tab: string) => {
    setActiveTab(tab as TabType);
  };
  const [activeTab, setActiveTab] = useState<TabType>('1');

  const [personalData, setPersonalData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    mobile: '',
  });
  const [vinData, setVinData] = useState({
    vinNumber: '',
    vehicleInfo: {} as VehicleInfo,
  });
  const [passwordData, setPasswordData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [userData, setUserData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    mobile: '',
    password: '',
  });

  useEffect(() => {
    setUserData({
      firstName: personalData.firstName,
      lastName: personalData.lastName,
      email: personalData.email,
      mobile: personalData.mobile,
      password: passwordData.password,
    });
  }, [personalData, passwordData]);

  const validateEmail = (email: string) => {
    const re = /\S+@\S+\.\S+/;
    return re.test(email);
  };

  const validateMobile = (mobile: string) => {
    const cleanMobile = mobile.replace(/\D/g, '');
    return cleanMobile.length === 10;
  };

  const isFirstThreeTabsValid = () => {
    const personalValid =
      personalData.firstName.trim() !== '' &&
      personalData.lastName.trim() !== '' &&
      validateEmail(personalData.email.trim()) &&
      validateMobile(personalData.mobile.trim());

    const passwordValid =
      passwordData.password.trim().length >= 8 &&
      passwordData.confirmPassword.trim() !== '' &&
      passwordData.password === passwordData.confirmPassword &&
      /[A-Z]/.test(passwordData.password) &&
      /[a-z]/.test(passwordData.password) &&
      /\d/.test(passwordData.password);

    const vinValid = vinData.vehicleInfo.valid === true;

    return personalValid && passwordValid && vinValid;
  };

  const handleTabPress = (tab: TabType) => {
    if (tab === '4') {
      if (!isFirstThreeTabsValid()) {
        Alert.alert(
          'Incomplete Information',
          'Please complete all required information in the first three steps before proceeding.',
        );
        return;
      }
    }
    setActiveTab(tab);
  };

  const handleBackPress = () => {
    navigation.navigate('LoginMainScreen' as never);
  };

  const renderTabs = () => {
    return (
      <View style={styles.tabsContainer}>
        {Object.entries(registrationTabData).map(([key, label]) => (
          <CustomTab
            key={key}
            label={label}
            active={activeTab === key}
            onPress={() => handleTabPress(key as TabType)}
            disabled={key === '4' && !isFirstThreeTabsValid()}
          />
        ))}
      </View>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case '1':
        return (
          <RegistrationPersonalDetails
            personalData={personalData}
            setPersonalData={setPersonalData}
            onContinue={() => setActiveTab('2')}
          />
        );
      case '2':
        return (
          <RegistrationCreatePassword
            personalData={personalData}
            passwordData={passwordData}
            setPasswordData={setPasswordData}
            setActiveTab={handleTabChange}
          />
        );
      case '3':
        return (
          <LoadVinScreen
            vinData={vinData}
            userData={userData}
            setVinData={setVinData}
            onContinue={() => setActiveTab('4')}
          />
        );
      case '4':
        return (
          <AccountCreatedScreen
            onContinue={() =>
              navigation.navigate('AppLoginPageScreen' as never)
            }
            vinData={vinData}
            userId={personalData.email}
          />
        );
      default:
        return (
          <RegistrationPersonalDetails
            personalData={personalData}
            setPersonalData={setPersonalData}
            onContinue={() => setActiveTab('2')}
          />
        );
    }
  };

  return (
    <View style={styles.mainContainer}>
      {/* Custom AppBar */}
      <AppBar
        showBackButton={true}
        showMailIcon={false}
        showChatIcon={false}
        showMenuIcon={false}
        showLogo={true}
        onBackPress={handleBackPress}
      />

      <ScreenLayout
        useScrollView={false}
        useImageBackground={true}
        centerContent={false}>
        {/* Title Section */}
        <TitleSection
          title={AppStrings.MCX_ACCOUNT_REGISTRATION_TITLE}
          bgColor={Colors.PRIMARY}
          textColor="#fff"
          style={styles.titleSection}
        />
        <KeyboardAvoidingView
          style={{flex: 1}}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}>
          <View style={styles.container}>
            {renderTabs()}
            <View style={styles.contentContainer}>{renderTabContent()}</View>
          </View>
        </KeyboardAvoidingView>
      </ScreenLayout>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.COMMON_TAB_SECTION_BG_COLOR,
    justifyContent: 'space-around',
    marginHorizontal: 12,
    marginTop: 20,
  },
  contentContainer: {
    backgroundColor: 'transparent',
    flex: 1,
  },
});

export default AccountRegistration;
/* import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
  SafeAreaView,
  StatusBar,
  Animated,
  Text,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import TitleSection from '../../../components/common/TitleSection';
import CustomTab from '../../../components/common/CustomTabs';
import AppBar from '../../../components/common/AppBar';
import {registrationTabData} from '../../../utils/templates/TemplateConfig';
import {AppStrings} from '../../../utils/constants/AppStrings';
import {Colors, Fonts} from '../../../utils/constants/Theme';
import RegistrationPersonalDetails from './components/RegistrationPersonalDetails';
import RegistrationCreatePassword from './components/RegistrationCreatePassword';
import LoadVinScreen from './components/LoadVinScreen';
import AccountCreatedScreen from './components/AccountCreatedScreen';

const {width, height} = Dimensions.get('window');

type TabType = keyof typeof registrationTabData;

interface VehicleInfo {
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
  warning: string;
  error: string;
  valid: boolean;
}

const AccountRegistration = () => {
  const navigation = useNavigation();

  // Animation values
  const [fadeAnim] = useState(new Animated.Value(0));
  const [progressAnim] = useState(new Animated.Value(0));

  const handleTabChange = (tab: string) => {
    setActiveTab(tab as TabType);
  };

  const [activeTab, setActiveTab] = useState<TabType>('1');

  const [personalData, setPersonalData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    mobile: '',
  });

  const [vinData, setVinData] = useState({
    vinNumber: '',
    vehicleInfo: {} as VehicleInfo,
  });

  const [passwordData, setPasswordData] = useState({
    password: '',
    confirmPassword: '',
  });

  const [userData, setUserData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    mobile: '',
    password: '',
  });

  useEffect(() => {
    // Entrance animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();

    // Update progress animation based on active tab
    const progress = (parseInt(activeTab) - 1) / 3;
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [activeTab]);

  useEffect(() => {
    setUserData({
      firstName: personalData.firstName,
      lastName: personalData.lastName,
      email: personalData.email,
      mobile: personalData.mobile,
      password: passwordData.password,
    });
  }, [personalData, passwordData]);

  const validateEmail = (email: string) => {
    const re = /\S+@\S+\.\S+/;
    return re.test(email);
  };

  const validateMobile = (mobile: string) => {
    const cleanMobile = mobile.replace(/\D/g, '');
    return cleanMobile.length === 10;
  };

  const isFirstThreeTabsValid = () => {
    const personalValid =
      personalData.firstName.trim() !== '' &&
      personalData.lastName.trim() !== '' &&
      validateEmail(personalData.email.trim()) &&
      validateMobile(personalData.mobile.trim());

    const passwordValid =
      passwordData.password.trim().length >= 8 &&
      passwordData.confirmPassword.trim() !== '' &&
      passwordData.password === passwordData.confirmPassword &&
      /[A-Z]/.test(passwordData.password) &&
      /[a-z]/.test(passwordData.password) &&
      /\d/.test(passwordData.password);

    const vinValid = vinData.vehicleInfo.valid === true;

    return personalValid && passwordValid && vinValid;
  };

  const handleTabPress = (tab: TabType) => {
    if (tab === '4') {
      if (!isFirstThreeTabsValid()) {
        Alert.alert(
          'Incomplete Information',
          'Please complete all required information in the first three steps before proceeding.',
        );
        return;
      }
    }
    setActiveTab(tab);
  };

  const handleBackPress = () => {
    navigation.navigate('LoginMainScreen' as never);
  };

  const getStepIcon = (step: string) => {
    switch (step) {
      case '1':
        return 'account-outline';
      case '2':
        return 'lock-outline';
      case '3':
        return 'car-outline';
      case '4':
        return 'check-circle-outline';
      default:
        return 'circle-outline';
    }
  };

  const getStepTitle = (step: string) => {
    switch (step) {
      case '1':
        return 'Personal Info';
      case '2':
        return 'Password';
      case '3':
        return 'Vehicle Details';
      case '4':
        return 'Complete';
      default:
        return '';
    }
  };

  const renderModernTabs = () => {
    const steps = Object.keys(registrationTabData);

    return (
      <View style={styles.modernTabsContainer}>
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarBackground}>
            <Animated.View
              style={[
                styles.progressBarFill,
                {
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>
        </View>

        <View style={styles.stepsContainer}>
          {steps.map((step, index) => {
            const isActive = activeTab === step;
            const isCompleted = parseInt(activeTab) > parseInt(step);
            const isDisabled = step === '4' && !isFirstThreeTabsValid();

            return (
              <TouchableOpacity
                key={step}
                style={styles.stepContainer}
                onPress={() => handleTabPress(step as TabType)}
                disabled={isDisabled}
                activeOpacity={0.7}>
                <View
                  style={[
                    styles.stepIndicator,
                    isActive && styles.stepIndicatorActive,
                    isCompleted && styles.stepIndicatorCompleted,
                    isDisabled && styles.stepIndicatorDisabled,
                  ]}>
                  {isCompleted ? (
                    <Icon name="check" size={16} color="white" />
                  ) : (
                    <Icon
                      name={getStepIcon(step)}
                      size={16}
                      color={
                        isActive ? 'white' : isDisabled ? '#666' : '#a0a0a0'
                      }
                    />
                  )}
                </View>
                <Text
                  style={[
                    styles.stepTitle,
                    isActive && styles.stepTitleActive,
                    isCompleted && styles.stepTitleCompleted,
                    isDisabled && styles.stepTitleDisabled,
                  ]}>
                  {getStepTitle(step)}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case '1':
        return (
          <RegistrationPersonalDetails
            personalData={personalData}
            setPersonalData={setPersonalData}
            onContinue={() => setActiveTab('2')}
          />
        );
      case '2':
        return (
          <RegistrationCreatePassword
            personalData={personalData}
            passwordData={passwordData}
            setPasswordData={setPasswordData}
            setActiveTab={handleTabChange}
          />
        );
      case '3':
        return (
          <LoadVinScreen
            vinData={vinData}
            userData={userData}
            setVinData={setVinData}
            onContinue={() => setActiveTab('4')}
          />
        );
      case '4':
        return (
          <AccountCreatedScreen
            onContinue={() =>
              navigation.navigate('AppLoginPageScreen' as never)
            }
            vinData={vinData}
            userId={personalData.email}
          />
        );
      default:
        return (
          <RegistrationPersonalDetails
            personalData={personalData}
            setPersonalData={setPersonalData}
            onContinue={() => setActiveTab('2')}
          />
        );
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />

      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.gradientBackground}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 1}}>
        <View style={[styles.floatingCircle, styles.circle1]} />
        <View style={[styles.floatingCircle, styles.circle2]} />
        <View style={[styles.floatingCircle, styles.circle3]} />

        <Animated.View style={[styles.container, {opacity: fadeAnim}]}>
          <View style={styles.headerContainer}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={handleBackPress}>
              <Icon name="arrow-left" size={24} color="white" />
            </TouchableOpacity>

            <View style={styles.headerContent}>
              <Text style={styles.headerTitle}>Create Account</Text>
              <Text style={styles.headerSubtitle}>
                Join our community of vehicle owners
              </Text>
            </View>
          </View>

          <KeyboardAvoidingView
            style={styles.keyboardAvoidingView}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}>
            <View style={styles.mainContent}>
              {renderModernTabs()}

              <View style={styles.contentContainer}>{renderTabContent()}</View>
            </View>
          </KeyboardAvoidingView>
        </Animated.View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  gradientBackground: {
    flex: 1,
    position: 'relative',
  },
  floatingCircle: {
    position: 'absolute',
    borderRadius: 100,
    opacity: 0.08,
  },
  circle1: {
    width: 200,
    height: 200,
    backgroundColor: '#ff6b6b',
    top: -50,
    right: -50,
  },
  circle2: {
    width: 150,
    height: 150,
    backgroundColor: '#74b9ff',
    bottom: 150,
    left: -40,
  },
  circle3: {
    width: 120,
    height: 120,
    backgroundColor: '#00b894',
    top: height * 0.4,
    right: -30,
  },
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 30,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: 'white',
    fontFamily: Fonts.ROBO_BOLD,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#a0a0a0',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  mainContent: {
    flex: 1,
  },
  modernTabsContainer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  progressBarContainer: {
    marginBottom: 24,
  },
  progressBarBackground: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: '#ff6b6b',
    borderRadius: 2,
  },
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stepContainer: {
    alignItems: 'center',
    flex: 1,
  },
  stepIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  stepIndicatorActive: {
    backgroundColor: '#ff6b6b',
    borderColor: '#ff6b6b',
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.4,
    shadowRadius: 8,
    elevation: 8,
  },
  stepIndicatorCompleted: {
    backgroundColor: '#00b894',
    borderColor: '#00b894',
  },
  stepIndicatorDisabled: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  stepTitle: {
    fontSize: 12,
    color: '#a0a0a0',
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
  },
  stepTitleActive: {
    color: 'white',
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  stepTitleCompleted: {
    color: '#00b894',
    fontWeight: '500',
  },
  stepTitleDisabled: {
    color: '#666',
  },
  contentContainer: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    marginHorizontal: 24,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 24,
    paddingHorizontal: 24,
  },
});

export default AccountRegistration;
 */
