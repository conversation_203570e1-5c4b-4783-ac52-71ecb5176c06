import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';
import { AppStrings } from '../../../../utils/constants/AppStrings';
import { Animated, StyleSheet, Text, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import React from 'react';

interface VinDetailsComponentProps {
  warning: string;
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
}

const VinDetailsComponent: React.FC<VinDetailsComponentProps> = ({
  warning,
  vin,
  make,
  model,
  manufactureYear,
  fuel,
}) => {
  const [fadeAnim] = React.useState(new Animated.Value(0));

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const renderDetailRow = (
    icon: string,
    label: string,
    value: string,
    color?: string,
  ) => (
    <View style={styles.vinDetailRow}>
      <View style={styles.vinDetailLeft}>
        <Icon
          name={icon}
          size={18}
          color={color || '#ff6b6b'}
          style={styles.vinDetailIcon}
        />
        <Text style={styles.vinDetailLabel}>{label}:</Text>
      </View>
      <Text style={[styles.vinDetailValue, color && {color}]}>{value}</Text>
    </View>
  );

  return (
    <Animated.View style={[styles.vinContainer, {opacity: fadeAnim}]}>
      {warning ? (
        <View style={styles.warningContainer}>
          <Icon
            name="alert-circle-outline"
            size={20}
            color="#856404"
            style={styles.warningIcon}
          />
          <Text style={styles.warningText}>{warning}</Text>
        </View>
      ) : null}

      <View style={styles.vinDetailsCard}>
        <View style={styles.vinCardHeader}>
          <Icon name="car-info" size={24} color="#ff6b6b" />
          <Text style={styles.vinCardTitle}>{AppStrings.MCX_VEHICLE_INFO_TITLE}</Text>
        </View>

        {renderDetailRow('identifier', 'VIN', vin)}
        {renderDetailRow('car', 'Make', make)}
        {renderDetailRow('car-side', 'Model', model)}
        {renderDetailRow('calendar', 'Year', manufactureYear)}
        {renderDetailRow('gas-station', 'Fuel Type', fuel)}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  // Common Styles
  container: {
    flex: 1,
  },
  animatedContainer: {
    flex: 1,
  },
  headerSection: {
    alignItems: 'center',
    paddingVertical: 24,
    paddingHorizontal: 24,
  },
  headerIcon: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    fontFamily: Fonts.ROBO_REGULAR,
  },

  // Personal Details Styles
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: 120,
  },
  formSection: {
    paddingHorizontal: 24,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    color: 'white',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: '600',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    height: 56,
  },
  inputWrapperFocused: {
    borderColor: '#ff6b6b',
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  inputWrapperError: {
    borderColor: '#ff6b6b',
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  inputIcon: {
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    color: 'white',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
    paddingVertical: 0,
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 14,
    marginTop: 8,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 24,
    paddingBottom: 30,
  },
  continueBtn: {
    borderRadius: 16,
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 16,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  buttonIcon: {
    marginLeft: 12,
  },

  // VIN Details Styles
  vinContainer: {
    marginHorizontal: 24,
    marginTop: 16,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 193, 7, 0.1)',
    borderColor: 'rgba(255, 193, 7, 0.3)',
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  warningIcon: {
    marginRight: 12,
  },
  warningText: {
    flex: 1,
    color: '#ffc107',
    fontSize: 14,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  vinDetailsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  vinCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  vinCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginLeft: 12,
    fontFamily: Fonts.ROBO_BOLD,
  },
  vinDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  vinDetailLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  vinDetailIcon: {
    marginRight: 8,
  },
  vinDetailLabel: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  vinDetailValue: {
    fontSize: 16,
    color: 'white',
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: '600',
  },

  // VIN Screen Styles
  vinScreenContainer: {
    flex: 1,
  },
  vinInputSection: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  vinInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    height: 56,
    marginBottom: 16,
  },
  vinInput: {
    flex: 1,
    color: 'white',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
    paddingVertical: 0,
  },
  loadVinBtn: {
    borderRadius: 12,
  },
  loadButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  loadButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  rotatingIcon: {
    // Add rotation animation if needed
  },
  vinBottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 24,
    paddingBottom: 30,
    gap: 12,
  },
  skipBtn: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  skipBtnText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  disabledBtn: {
    opacity: 0.5,
  },

  // Success Screen Styles
  successContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  successContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successIconContainer: {
    marginBottom: 32,
  },
  successIconGradient: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successTitle: {
    fontSize: 32,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
  },
  successSubtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    marginBottom: 40,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  featuresContainer: {
    width: '100%',
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    textAlign: 'center',
    marginBottom: 24,
    fontFamily: Fonts.ROBO_BOLD,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    marginBottom: 12,
  },
  featureIcon: {
    marginRight: 16,
  },
  featureText: {
    flex: 1,
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  getStartedBtn: {
    borderRadius: 16,
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
});

export default VinDetailsComponent;
