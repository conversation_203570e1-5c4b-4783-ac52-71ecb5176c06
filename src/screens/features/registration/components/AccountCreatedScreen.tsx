import React from 'react';
import {View, Text, StyleSheet, ScrollView, Alert} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {VehicleService} from '../../../../utils/services/VehicleService';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import CustomButton from '../../../../components/common/CustomButton';
import {AppStrings} from '../../../../utils/constants/AppStrings';
import {Colors, Fonts, Sizes} from '../../../../utils/constants/Theme';

interface AccountCreatedScreenProps {
  onContinue: () => void;
}

interface AccountCreatedScreenProps {
  onContinue: () => void;
  vinData?: {
    vinNumber: string;
    vehicleInfo: {
      vin: string;
      make: string;
      model: string;
      manufactureYear: string;
      fuel: string;
      warning?: string;
      valid?: boolean;
    };
  };
  userId?: string;
}

const AccountCreatedScreen: React.FC<AccountCreatedScreenProps> = ({
  onContinue,
  vinData,
}) => {
  const handleContinue = async () => {
    try {
      const userId = await AsyncStorage.getItem('userId');
      if (vinData?.vehicleInfo.valid && userId) {
        const {vehicleInfo} = vinData;
        await VehicleService.addVehicleInfo(userId, {
          vin: vehicleInfo.vin,
          make: vehicleInfo.make,
          model: vehicleInfo.model,
          manufactureYear: vehicleInfo.manufactureYear,
          fuel: vehicleInfo.fuel,
          warning: vehicleInfo.warning,
        });
      }
      await AsyncStorage.removeItem('userId');
      onContinue();
    } catch (error: any) {
      Alert.alert(
        'Error',
        'Failed to save vehicle information. You can add it later from your profile.',
      );
      onContinue();
    } finally {
    }
  };
  return (
    <View style={styles.container}>
      <View style={styles.accountCreatedSection}>
        <RegistrationTitleSection
          title="ACCOUNT CREATED"
          backgroundColor="#FFFFFF"
          borderBottomWidth={1}
          borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
          paddingVertical={16}
          paddingHorizontal={0}
        />
        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}>
          <Text style={styles.paragraph}>
            myCANx app for Consumers – a versatile enterprise solution powered
            by a smartphone app that allows mechanics and consumers to interact
            quickly and effectively. During travel or their daily commute,
            consumers can reach out to mechanics nearest to them, for problems
            related to their vehicles, wherever they may be!
          </Text>
          <Text style={styles.paragraph}>
            The myCANx app for Consumers is a peer-to-peer marketplace for
            on-demand mobile mechanics, or skillful auto technicians. myCANx
            provides priceless convenience to consumers, all with the touch of a
            button. At the consumer's request, a mobile mechanic in the myCANx
            network reaches the consumer's door and takes care of the repairs
            and other services, such as oil change, roadside assistance, fuel
            delivery, etc.
          </Text>
          <Text style={styles.paragraph}>{AppStrings.MCX_ADVANTAGES_TEXT}</Text>
          <Text style={styles.paragraph}>
            {'\u00A0\u00A0'} Roadside Assistance
          </Text>
          <Text style={styles.paragraph}>
            myCANx consumers can reach out to myCANx mechanics to take care of
            their flat tires, fuel deliveries, or jump starts.
          </Text>
          <Text style={styles.paragraph}>
            {'\u00A0\u00A0'} Diagnostics and Inspections
          </Text>
          <Text style={styles.paragraph}>
            myCANx mechanics can help myCANx consumers with their pre-purchase
            inspection for new purchases or leases on a vehicle. The mechanics
            can also check engine lights, fluids and filters, wipers, and more.
          </Text>
          <Text style={styles.paragraph}>{'\u00A0\u00A0'} Repair</Text>
          <Text style={styles.paragraph}>
            myCANx consumers can book a service appointment and each vehicle
            will be inspected, diagnosed, and handled with care by the trusted
            and experienced myCANx technicians. Everything from out of tune
            belts, brakes to hoses will be taken care of.
          </Text>
        </ScrollView>
      </View>
      <View style={styles.bottomContainer}>
        <CustomButton
          text={AppStrings.MCX_CONTINUE_BUTTON}
          onPress={handleContinue}
          variant="primary"
          size="large"
          fullWidth={true}
          backgroundColor={Colors.SECONDARY}
          textColor="#fff"
          isBoldText={true}
          isBottomButton={true}
          bottomLineWidth={1}
          bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
    justifyContent: 'flex-start',
  },
  accountCreatedSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    borderRadius: 2,
    alignSelf: 'stretch',
    flex: 1,
    overflow: 'hidden',
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 80,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  paragraph: {
    fontSize: Sizes.MEDIUM,
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    lineHeight: 24,
    marginBottom: 15,
  },
});

export default AccountCreatedScreen;
/* import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  Animated,
  TouchableOpacity,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {VehicleService} from '../../../../utils/services/VehicleService';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import CustomButton from '../../../../components/common/CustomButton';
import {AppStrings} from '../../../../utils/constants/AppStrings';
import {Colors, Fonts, Sizes} from '../../../../utils/constants/Theme';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

interface AccountCreatedScreenProps {
  onContinue: () => void;
}

interface AccountCreatedScreenProps {
  onContinue: () => void;
  vinData?: {
    vinNumber: string;
    vehicleInfo: {
      vin: string;
      make: string;
      model: string;
      manufactureYear: string;
      fuel: string;
      warning?: string;
      valid?: boolean;
    };
  };
  userId?: string;
}

const AccountCreatedScreen: React.FC<AccountCreatedScreenProps> = ({
  onContinue,
  vinData,
}) => {
  const [fadeAnim] = React.useState(new Animated.Value(0));
  const [scaleAnim] = React.useState(new Animated.Value(0.8));

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <Animated.View style={[styles.successContainer, {opacity: fadeAnim}]}>
      <Animated.View
        style={[styles.successContent, {transform: [{scale: scaleAnim}]}]}>
        <View style={styles.successIconContainer}>
          <LinearGradient
            colors={['#00b894', '#00a085']}
            style={styles.successIconGradient}>
            <Icon name="check-circle" size={64} color="white" />
          </LinearGradient>
        </View>

        <Text style={styles.successTitle}>{AppStrings.MCX_ACCOUNT_CREATED_TITLE}</Text>
        <Text style={styles.successSubtitle}>{AppStrings.MCX_WELCOME_TITLE}</Text>

        <View style={styles.featuresContainer}>
          <Text style={styles.featuresTitle}>{AppStrings.MCX_FEATURES_INTRO_TEXT}</Text>

          <View style={styles.featureItem}>
            <Icon
              name="road"
              size={20}
              color="#ff6b6b"
              style={styles.featureIcon}
            />
            <Text style={styles.featureText}>
              Get roadside assistance anytime, anywhere
            </Text>
          </View>

          <View style={styles.featureItem}>
            <Icon
              name="car-wrench"
              size={20}
              color="#ff6b6b"
              style={styles.featureIcon}
            />
            <Text style={styles.featureText}>
              Connect with trusted mechanics near you
            </Text>
          </View>

          <View style={styles.featureItem}>
            <Icon
              name="clipboard-check"
              size={20}
              color="#ff6b6b"
              style={styles.featureIcon}
            />
            <Text style={styles.featureText}>
              Schedule diagnostics and inspections
            </Text>
          </View>

          <View style={styles.featureItem}>
            <Icon
              name="tools"
              size={20}
              color="#ff6b6b"
              style={styles.featureIcon}
            />
            <Text style={styles.featureText}>
              Book repair services at your convenience
            </Text>
          </View>
        </View>
      </Animated.View>

      <View style={styles.bottomContainer}>
        <TouchableOpacity
          style={styles.getStartedBtn}
          onPress={() => {
          }}
          activeOpacity={0.8}>
          <LinearGradient
            colors={['#ff6b6b', '#ee5a24']}
            style={styles.buttonGradient}>
            <Text style={styles.buttonText}>{AppStrings.MCX_GET_STARTED_TEXT}</Text>
            <Icon
              name="rocket"
              size={20}
              color="white"
              style={styles.buttonIcon}
            />
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  // Common Styles
  container: {
    flex: 1,
  },
  animatedContainer: {
    flex: 1,
  },
  headerSection: {
    alignItems: 'center',
    paddingVertical: 24,
    paddingHorizontal: 24,
  },
  headerIcon: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    fontFamily: Fonts.ROBO_REGULAR,
  },

  // Personal Details Styles
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: 120,
  },
  formSection: {
    paddingHorizontal: 24,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    color: 'white',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: '600',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    height: 56,
  },
  inputWrapperFocused: {
    borderColor: '#ff6b6b',
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  inputWrapperError: {
    borderColor: '#ff6b6b',
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  inputIcon: {
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    color: 'white',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
    paddingVertical: 0,
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 14,
    marginTop: 8,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 24,
    paddingBottom: 30,
  },
  continueBtn: {
    borderRadius: 16,
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 16,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  buttonIcon: {
    marginLeft: 12,
  },

  // VIN Details Styles
  vinContainer: {
    marginHorizontal: 24,
    marginTop: 16,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 193, 7, 0.1)',
    borderColor: 'rgba(255, 193, 7, 0.3)',
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  warningIcon: {
    marginRight: 12,
  },
  warningText: {
    flex: 1,
    color: '#ffc107',
    fontSize: 14,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  vinDetailsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  vinCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  vinCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginLeft: 12,
    fontFamily: Fonts.ROBO_BOLD,
  },
  vinDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  vinDetailLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  vinDetailIcon: {
    marginRight: 8,
  },
  vinDetailLabel: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  vinDetailValue: {
    fontSize: 16,
    color: 'white',
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: '600',
  },

  // VIN Screen Styles
  vinScreenContainer: {
    flex: 1,
  },
  vinInputSection: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  vinInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    height: 56,
    marginBottom: 16,
  },
  vinInput: {
    flex: 1,
    color: 'white',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
    paddingVertical: 0,
  },
  loadVinBtn: {
    borderRadius: 12,
  },
  loadButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  loadButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  rotatingIcon: {
    // Add rotation animation if needed
  },
  vinBottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 24,
    paddingBottom: 30,
    gap: 12,
  },
  skipBtn: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  skipBtnText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  disabledBtn: {
    opacity: 0.5,
  },

  // Success Screen Styles
  successContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  successContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successIconContainer: {
    marginBottom: 32,
  },
  successIconGradient: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successTitle: {
    fontSize: 32,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
  },
  successSubtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    marginBottom: 40,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  featuresContainer: {
    width: '100%',
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    textAlign: 'center',
    marginBottom: 24,
    fontFamily: Fonts.ROBO_BOLD,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    marginBottom: 12,
  },
  featureIcon: {
    marginRight: 16,
  },
  featureText: {
    flex: 1,
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  getStartedBtn: {
    borderRadius: 16,
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
});

export default AccountCreatedScreen;
 */
