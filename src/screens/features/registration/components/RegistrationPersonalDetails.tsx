import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Alert,
} from 'react-native';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import {Colors, Fonts, Sizes} from '../../../../utils/constants/Theme';
import {AppStrings} from '../../../../utils/constants/AppStrings';

interface RegistrationPersonalDetailsProps {
  personalData: {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
  };
  setPersonalData: (data: {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
  }) => void;
  onContinue: () => void;
}

const RegistrationPersonalDetails: React.FC<
  RegistrationPersonalDetailsProps
> = ({personalData, setPersonalData, onContinue}) => {
  const [errors, setErrors] = React.useState<{
    firstName?: string;
    lastName?: string;
    email?: string;
    mobile?: string;
  }>({});
  const [loading, setLoading] = React.useState(false);

  const validateMobile = (mobile: string) => {
    const cleanMobile = mobile.replace(/\D/g, '');
    if (cleanMobile.length !== 10) {
      return 'Enter a valid 10-digit mobile number.';
    }
    return '';
  };

  const validateField = (field: string, value: string) => {
    let error = '';
    if (field === 'firstName' && !value.trim()) {
      error = 'Enter first name.';
    } else if (field === 'lastName' && !value.trim()) {
      error = 'Enter last name.';
    } else if (field === 'email') {
      if (!value.trim()) {
        error = 'Enter a email.';
      } else if (!/\S+@\S+\.\S+/.test(value)) {
        error = 'Enter a valid email.';
      }
    } else if (field === 'mobile') {
      if (!value.trim()) {
        error = 'Enter a mobile number.';
      } else {
        error = validateMobile(value);
      }
    }
    setErrors(prev => ({...prev, [field]: error}));
  };

  const validate = () => {
    const newErrors: {
      firstName?: string;
      lastName?: string;
      email?: string;
      mobile?: string;
    } = {};

    if (!personalData.firstName.trim()) {
      newErrors.firstName = 'Enter first name.';
    }

    if (!personalData.lastName.trim()) {
      newErrors.lastName = 'Enter last name.';
    }

    if (!personalData.email.trim()) {
      newErrors.email = 'Enter a email.';
    } else if (!/\S+@\S+\.\S+/.test(personalData.email)) {
      newErrors.email = 'Enter a valid email.';
    }

    if (!personalData.mobile.trim()) {
      newErrors.mobile = 'Enter a mobile number.';
    } else {
      const mobileError = validateMobile(personalData.mobile);
      if (mobileError) {
        newErrors.mobile = mobileError;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleContinue = async () => {
    if (!validate()) {
      return;
    }
    setLoading(true);
    try {
      onContinue();
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.message || 'Failed to save personal information',
      );
    } finally {
      setLoading(false);
    }
  };

  const scrollViewRef = React.useRef<ScrollView>(null);

  const handleInputFocus = (fieldName: string) => {
    setTimeout(() => {
      switch (fieldName) {
        case 'mobile':
          scrollViewRef.current?.scrollTo({y: 400, animated: true});
          break;
        case 'email':
          scrollViewRef.current?.scrollTo({y: 300, animated: true});
          break;
        case 'lastName':
          scrollViewRef.current?.scrollTo({y: 150, animated: true});
          break;
        default:
          scrollViewRef.current?.scrollTo({y: 0, animated: true});
      }
    }, 300);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 80}>
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        bounces={false}>
        <View style={styles.formSection}>
          <RegistrationTitleSection
            title="PERSONAL INFORMATION"
            backgroundColor="#FFFFFF"
            borderBottomWidth={1}
            borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
            paddingVertical={16}
            paddingHorizontal={0}
          />

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{AppStrings.MCX_FIRST_NAME_TITLE}</Text>
            <CommonTextInput
              value={personalData.firstName}
              onChangeText={text => {
                setPersonalData({...personalData, firstName: text});
                validateField('firstName', text);
              }}
              placeholder="Enter your first name"
              style={styles.textInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
              onFocus={() => handleInputFocus('firstName')}
            />
            {errors.firstName && (
              <Text style={styles.errorText}>{errors.firstName}</Text>
            )}
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{AppStrings.MCX_LAST_NAME_TITLE}</Text>
            <CommonTextInput
              value={personalData.lastName}
              onChangeText={text => {
                setPersonalData({...personalData, lastName: text});
                validateField('lastName', text);
              }}
              placeholder="Enter your last name"
              style={styles.textInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
              onFocus={() => handleInputFocus('lastName')}
            />
            {errors.lastName && (
              <Text style={styles.errorText}>{errors.lastName}</Text>
            )}
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{AppStrings.MCX_EMAIL_INPUT_TITLE}</Text>
            <CommonTextInput
              value={personalData.email}
              onChangeText={text => {
                setPersonalData({...personalData, email: text});
                validateField('email', text);
              }}
              placeholder="Enter your email address"
              style={styles.textInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
              keyboardType="email-address"
              onFocus={() => handleInputFocus('email')}
            />
            {errors.email && (
              <Text style={styles.errorText}>{errors.email}</Text>
            )}
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{AppStrings.MCX_MOBILE_NUMBER_TITLE}</Text>
            <CommonTextInput
              value={personalData.mobile}
              onChangeText={text => {
                const cleaned = text.replace(/\D/g, '');
                let formatted = cleaned;
                if (cleaned.length >= 6) {
                  formatted = `(${cleaned.slice(0, 3)}) ${cleaned.slice(
                    3,
                    6,
                  )}-${cleaned.slice(6, 10)}`;
                } else if (cleaned.length >= 3) {
                  formatted = `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
                }
                setPersonalData({...personalData, mobile: formatted});
                validateField('mobile', formatted);
              }}
              placeholder="(*************"
              style={styles.textInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
              keyboardType="phone-pad"
              maxLength={14}
              onFocus={() => handleInputFocus('mobile')}
            />
            {errors.mobile && (
              <Text style={styles.errorText}>{errors.mobile}</Text>
            )}
          </View>
        </View>
        <View style={styles.bottomSpacing} />
      </ScrollView>

      <View style={styles.bottomContainer}>
        <CustomButton
          text={loading ? 'Saving...' : AppStrings.MCX_CONTINUE_BUTTON}
          onPress={handleContinue}
          variant="primary"
          size="large"
          fullWidth={true}
          backgroundColor={Colors.SECONDARY}
          textColor="#fff"
          isBoldText={true}
          isBottomButton={true}
          bottomLineWidth={1}
          bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
          disabled={loading}
        />
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingHorizontal: 0,
    paddingBottom: Platform.OS === 'ios' ? 50 : 30,
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    borderRadius: 2,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: Sizes.LARGE,
    fontWeight: '600',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 16,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  inputLabel: {
    fontSize: Sizes.MEDIUM,
    fontWeight: '500',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    backgroundColor: '#FFFFFF',
  },
  errorText: {
    color: Colors.PRIMARY,
    fontSize: Sizes.SMALL,
    marginTop: 4,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
  },
  bottomSpacing: {
    height: Platform.OS === 'ios' ? 150 : 120,
  },
  continueButton: {
    borderRadius: 4,
  },
});

export default RegistrationPersonalDetails;
/* import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Alert,
  TouchableOpacity,
  TextInput,
  Animated,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import {Colors, Fonts, Sizes} from '../../../../utils/constants/Theme';
import {AppStrings} from '../../../../utils/constants/AppStrings';

interface RegistrationPersonalDetailsProps {
  personalData: {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
  };
  setPersonalData: (data: {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
  }) => void;
  onContinue: () => void;
}

const RegistrationPersonalDetails: React.FC<
  RegistrationPersonalDetailsProps
> = ({personalData, setPersonalData, onContinue}) => {
  const [errors, setErrors] = React.useState<{
    firstName?: string;
    lastName?: string;
    email?: string;
    mobile?: string;
  }>({});
  const [loading, setLoading] = React.useState(false);
  const [focusedField, setFocusedField] = React.useState<string | null>(null);
  const [fadeAnim] = React.useState(new Animated.Value(0));

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  const validateMobile = (mobile: string) => {
    const cleanMobile = mobile.replace(/\D/g, '');
    if (cleanMobile.length !== 10) {
      return 'Enter a valid 10-digit mobile number.';
    }
    return '';
  };

  const validateField = (field: string, value: string) => {
    let error = '';
    if (field === 'firstName' && !value.trim()) {
      error = 'Enter first name.';
    } else if (field === 'lastName' && !value.trim()) {
      error = 'Enter last name.';
    } else if (field === 'email') {
      if (!value.trim()) {
        error = 'Enter a email.';
      } else if (!/\S+@\S+\.\S+/.test(value)) {
        error = 'Enter a valid email.';
      }
    } else if (field === 'mobile') {
      if (!value.trim()) {
        error = 'Enter a mobile number.';
      } else {
        error = validateMobile(value);
      }
    }
    setErrors(prev => ({...prev, [field]: error}));
  };

  const validate = () => {
    const newErrors: {
      firstName?: string;
      lastName?: string;
      email?: string;
      mobile?: string;
    } = {};

    if (!personalData.firstName.trim()) {
      newErrors.firstName = 'Enter first name.';
    }

    if (!personalData.lastName.trim()) {
      newErrors.lastName = 'Enter last name.';
    }

    if (!personalData.email.trim()) {
      newErrors.email = 'Enter a email.';
    } else if (!/\S+@\S+\.\S+/.test(personalData.email)) {
      newErrors.email = 'Enter a valid email.';
    }

    if (!personalData.mobile.trim()) {
      newErrors.mobile = 'Enter a mobile number.';
    } else {
      const mobileError = validateMobile(personalData.mobile);
      if (mobileError) {
        newErrors.mobile = mobileError;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleContinue = async () => {
    if (!validate()) {
      return;
    }
    setLoading(true);
    try {
      onContinue();
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.message || 'Failed to save personal information',
      );
    } finally {
      setLoading(false);
    }
  };

  const renderInputField = (
    field: keyof typeof personalData,
    label: string,
    placeholder: string,
    icon: string,
    keyboardType?: any,
  ) => {
    const isFocused = focusedField === field;
    const hasError = !!errors[field];

    return (
      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>{label}</Text>
        <View
          style={[
            styles.inputWrapper,
            isFocused && styles.inputWrapperFocused,
            hasError && styles.inputWrapperError,
          ]}>
          <Icon
            name={icon}
            size={20}
            color={isFocused ? '#ff6b6b' : '#a0a0a0'}
            style={styles.inputIcon}
          />
          <TextInput
            style={styles.textInput}
            value={personalData[field]}
            onChangeText={text => {
              if (field === 'mobile') {
                const cleaned = text.replace(/\D/g, '');
                let formatted = cleaned;
                if (cleaned.length >= 6) {
                  formatted = `(${cleaned.slice(0, 3)}) ${cleaned.slice(
                    3,
                    6,
                  )}-${cleaned.slice(6, 10)}`;
                } else if (cleaned.length >= 3) {
                  formatted = `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
                }
                setPersonalData({...personalData, [field]: formatted});
                validateField(field, formatted);
              } else {
                setPersonalData({...personalData, [field]: text});
                validateField(field, text);
              }
            }}
            placeholder={placeholder}
            placeholderTextColor="#666"
            keyboardType={keyboardType || 'default'}
            maxLength={field === 'mobile' ? 14 : undefined}
            onFocus={() => setFocusedField(field)}
            onBlur={() => setFocusedField(null)}
            autoCorrect={false}
            autoCapitalize={field === 'email' ? 'none' : 'words'}
          />
        </View>
        {errors[field] && <Text style={styles.errorText}>{errors[field]}</Text>}
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 80}>
      <Animated.View style={[styles.animatedContainer, {opacity: fadeAnim}]}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          <View style={styles.headerSection}>
            <Text style={styles.sectionTitle}>{AppStrings.MCX_PERSONAL_INFO_TITLE}</Text>
            <Text style={styles.sectionSubtitle}>
              Let's get to know you better
            </Text>
          </View>

          <View style={styles.formSection}>
            {renderInputField(
              'firstName',
              'First Name',
              'Enter your first name',
              'account-outline',
            )}
            {renderInputField(
              'lastName',
              'Last Name',
              'Enter your last name',
              'account-outline',
            )}
            {renderInputField(
              'email',
              'Email Address',
              'Enter your email address',
              'email-outline',
              'email-address',
            )}
            {renderInputField(
              'mobile',
              'Mobile Number',
              '(*************',
              'phone-outline',
              'phone-pad',
            )}
          </View>
        </ScrollView>

        <View style={styles.bottomContainer}>
          <TouchableOpacity
            style={styles.continueBtn}
            onPress={handleContinue}
            disabled={loading}
            activeOpacity={0.8}>
            <LinearGradient
              colors={loading ? ['#999', '#666'] : ['#ff6b6b', '#ee5a24']}
              style={styles.buttonGradient}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}>
              {loading ? (
                <Text style={styles.buttonText}>{AppStrings.MCX_PROCESSING_TEXT}</Text>
              ) : (
                <>
                  <Text style={styles.buttonText}>{AppStrings.MCX_CONTINUE_TEXT}</Text>
                  <Icon
                    name="arrow-right"
                    size={20}
                    color="white"
                    style={styles.buttonIcon}
                  />
                </>
              )}
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </Animated.View>
    </KeyboardAvoidingView>
  );
};
const styles = StyleSheet.create({
  // Common Styles
  container: {
    flex: 1,
  },
  animatedContainer: {
    flex: 1,
  },
  headerSection: {
    alignItems: 'center',
    paddingVertical: 24,
    paddingHorizontal: 24,
  },
  headerIcon: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    fontFamily: Fonts.ROBO_REGULAR,
  },

  // Personal Details Styles
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: 120,
  },
  formSection: {
    paddingHorizontal: 24,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    color: 'white',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: '600',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    height: 56,
  },
  inputWrapperFocused: {
    borderColor: '#ff6b6b',
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  inputWrapperError: {
    borderColor: '#ff6b6b',
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  inputIcon: {
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    color: 'white',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
    paddingVertical: 0,
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 14,
    marginTop: 8,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 24,
    paddingBottom: 30,
  },
  continueBtn: {
    borderRadius: 16,
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 16,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  buttonIcon: {
    marginLeft: 12,
  },

  // VIN Details Styles
  vinContainer: {
    marginHorizontal: 24,
    marginTop: 16,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 193, 7, 0.1)',
    borderColor: 'rgba(255, 193, 7, 0.3)',
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  warningIcon: {
    marginRight: 12,
  },
  warningText: {
    flex: 1,
    color: '#ffc107',
    fontSize: 14,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  vinDetailsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  vinCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  vinCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginLeft: 12,
    fontFamily: Fonts.ROBO_BOLD,
  },
  vinDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  vinDetailLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  vinDetailIcon: {
    marginRight: 8,
  },
  vinDetailLabel: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  vinDetailValue: {
    fontSize: 16,
    color: 'white',
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: '600',
  },

  // VIN Screen Styles
  vinScreenContainer: {
    flex: 1,
  },
  vinInputSection: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  vinInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    height: 56,
    marginBottom: 16,
  },
  vinInput: {
    flex: 1,
    color: 'white',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
    paddingVertical: 0,
  },
  loadVinBtn: {
    borderRadius: 12,
  },
  loadButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  loadButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  rotatingIcon: {
    // Add rotation animation if needed
  },
  vinBottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 24,
    paddingBottom: 30,
    gap: 12,
  },
  skipBtn: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  skipBtnText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  disabledBtn: {
    opacity: 0.5,
  },

  // Success Screen Styles
  successContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  successContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successIconContainer: {
    marginBottom: 32,
  },
  successIconGradient: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successTitle: {
    fontSize: 32,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
  },
  successSubtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    marginBottom: 40,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  featuresContainer: {
    width: '100%',
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    textAlign: 'center',
    marginBottom: 24,
    fontFamily: Fonts.ROBO_BOLD,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    marginBottom: 12,
  },
  featureIcon: {
    marginRight: 16,
  },
  featureText: {
    flex: 1,
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  getStartedBtn: {
    borderRadius: 16,
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
});

export default RegistrationPersonalDetails;
 */
