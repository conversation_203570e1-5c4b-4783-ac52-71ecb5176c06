import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import {Colors, Fonts, Sizes} from '../../../../utils/constants/Theme';
import {AppStrings} from '../../../../utils/constants/AppStrings';

interface RegistrationPasswordScreenProps {
  personalData: {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
  };
  passwordData: {password: string; confirmPassword: string};
  setPasswordData: (data: {password: string; confirmPassword: string}) => void;
  setActiveTab: (tab: string) => void;
}

const RegistrationPasswordScreen: React.FC<RegistrationPasswordScreenProps> = ({
  personalData: _personalData,
  passwordData,
  setPasswordData,
  setActiveTab,
}) => {
  const [errors, setErrors] = React.useState<{
    password?: string;
    confirmPassword?: string;
  }>({});
  const [isContinuing, setIsContinuing] = React.useState(false);

  const validatePassword = (password: string) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    if (password.length < minLength) {
      return 'Password must be at least 8 characters.';
    }
    if (!hasUpperCase) {
      return 'Password must contain at least one uppercase letter.';
    }
    if (!hasLowerCase) {
      return 'Password must contain at least one lowercase letter.';
    }
    if (!hasNumbers) {
      return 'Password must contain at least one number.';
    }
    if (!hasSpecialChar) {
      return 'Password must contain at least one special character.';
    }
    return '';
  };

  const handlePasswordChange = (text: string) => {
    const passwordError = validatePassword(text);
    setErrors(prevErrors => ({
      ...prevErrors,
      password: passwordError,
    }));
    setPasswordData({...passwordData, password: text});
  };

  const handleConfirmPasswordChange = (text: string) => {
    let confirmError = '';
    if (!text) {
      confirmError = 'Confirm your password.';
    } else if (text !== passwordData.password) {
      confirmError = 'Passwords do not match.';
    }
    setErrors(prevErrors => ({
      ...prevErrors,
      confirmPassword: confirmError,
    }));
    setPasswordData({...passwordData, confirmPassword: text});
  };

  const handleContinue = async () => {
    const passwordError = validatePassword(passwordData.password);
    const confirmError = !passwordData.confirmPassword
      ? 'Confirm your password.'
      : passwordData.password !== passwordData.confirmPassword
      ? 'Passwords do not match.'
      : '';
    if (passwordError || confirmError) {
      setErrors({
        password: passwordError,
        confirmPassword: confirmError,
      });
      return;
    }
    setActiveTab('3');
    setIsContinuing(true);
  };

  return (
    <View style={styles.container}>
      <View style={styles.formSection}>
        <RegistrationTitleSection
          title="CREATE PASSWORD"
          backgroundColor="#FFFFFF"
          borderBottomWidth={1}
          borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
          paddingVertical={16}
          paddingHorizontal={0}
        />

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>PASSWORD</Text>
          <CommonTextInput
            value={passwordData.password}
            onChangeText={handlePasswordChange}
            placeholder="Enter password"
            style={[styles.textInput, errors.password && styles.inputError]}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            secureTextEntry={true}
          />
          {errors.password && (
            <Text style={styles.errorText}>{errors.password}</Text>
          )}
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>CONFIRM PASSWORD</Text>
          <CommonTextInput
            value={passwordData.confirmPassword}
            onChangeText={handleConfirmPasswordChange}
            placeholder="Confirm password"
            style={[
              styles.textInput,
              errors.confirmPassword && styles.inputError,
            ]}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            secureTextEntry={true}
          />
          {errors.confirmPassword && (
            <Text style={styles.errorText}>{errors.confirmPassword}</Text>
          )}
        </View>
      </View>

      <View style={styles.bottomContainer}>
        <CustomButton
          text={AppStrings.MCX_CONTINUE_BUTTON}
          onPress={handleContinue}
          variant="primary"
          size="large"
          fullWidth={true}
          backgroundColor={Colors.SECONDARY}
          textColor="#fff"
          isBoldText={true}
          isBottomButton={true}
          bottomLineWidth={1}
          bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
          disabled={isContinuing}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
    justifyContent: 'flex-start',
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    borderRadius: 2,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: Sizes.LARGE,
    fontWeight: '600',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 20,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  inputLabel: {
    fontSize: Sizes.MEDIUM,
    fontWeight: '500',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    backgroundColor: '#FFFFFF',
  },
  inputError: {
    borderColor: Colors.PRIMARY,
  },
  errorText: {
    color: Colors.PRIMARY,
    fontSize: Sizes.SMALL,
    marginTop: 4,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  continueButton: {
    borderRadius: 4,
  },
});

export default RegistrationPasswordScreen;

/* import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/Feather';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import {Colors, Fonts, Sizes} from '../../../../utils/constants/Theme';
import {AppStrings} from '../../../../utils/constants/AppStrings';

interface RegistrationPasswordScreenProps {
  personalData: {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
  };
  passwordData: {password: string; confirmPassword: string};
  setPasswordData: (data: {password: string; confirmPassword: string}) => void;
  setActiveTab: (tab: string) => void;
}

const RegistrationPasswordScreen: React.FC<RegistrationPasswordScreenProps> = ({
  personalData: _personalData,
  passwordData,
  setPasswordData,
  setActiveTab,
}) => {
  const [errors, setErrors] = React.useState<{
    password?: string;
    confirmPassword?: string;
  }>({});
  const [isContinuing, setIsContinuing] = React.useState(false);
  const [showPassword, setShowPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);

  const validatePassword = (password: string) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    if (password.length < minLength) {
      return 'Password must be at least 8 characters.';
    }
    if (!hasUpperCase) {
      return 'Password must contain at least one uppercase letter.';
    }
    if (!hasLowerCase) {
      return 'Password must contain at least one lowercase letter.';
    }
    if (!hasNumbers) {
      return 'Password must contain at least one number.';
    }
    if (!hasSpecialChar) {
      return 'Password must contain at least one special character.';
    }
    return '';
  };

  const getPasswordStrength = (password: string) => {
    if (!password) return {strength: 0, color: '#666', text: ''};

    let score = 0;
    if (password.length >= 8) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[a-z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++;

    type StrengthKey = 0 | 1 | 2 | 3 | 4 | 5;
    const strengthMap: Record<
      StrengthKey,
      {strength: number; color: string; text: string}
    > = {
      0: {strength: 0, color: '#FF6B6B', text: ''},
      1: {strength: 20, color: '#FF6B6B', text: 'Very Weak'},
      2: {strength: 40, color: '#FFA726', text: 'Weak'},
      3: {strength: 60, color: '#FFD54F', text: 'Fair'},
      4: {strength: 80, color: '#66BB6A', text: 'Good'},
      5: {strength: 100, color: '#4CAF50', text: 'Strong'},
    };

    const key = (score > 5 ? 5 : score) as StrengthKey;
    return strengthMap[key];
  };

  const handlePasswordChange = (text: string) => {
    const passwordError = validatePassword(text);
    setErrors(prevErrors => ({
      ...prevErrors,
      password: passwordError,
    }));
    setPasswordData({...passwordData, password: text});
  };

  const handleConfirmPasswordChange = (text: string) => {
    let confirmError = '';
    if (!text) {
      confirmError = 'Confirm your password.';
    } else if (text !== passwordData.password) {
      confirmError = 'Passwords do not match.';
    }
    setErrors(prevErrors => ({
      ...prevErrors,
      confirmPassword: confirmError,
    }));
    setPasswordData({...passwordData, confirmPassword: text});
  };

  const handleContinue = async () => {
    const passwordError = validatePassword(passwordData.password);
    const confirmError = !passwordData.confirmPassword
      ? 'Confirm your password.'
      : passwordData.password !== passwordData.confirmPassword
      ? 'Passwords do not match.'
      : '';
    if (passwordError || confirmError) {
      setErrors({
        password: passwordError,
        confirmPassword: confirmError,
      });
      return;
    }
    setActiveTab('3');
    setIsContinuing(true);
  };

  const passwordStrength = getPasswordStrength(passwordData.password);

  return (
    <LinearGradient
      colors={['#1a1a2e', '#16213e', '#0f3460']}
      style={styles.container}
      start={{x: 0, y: 0}}
      end={{x: 1, y: 1}}>
      <View style={[styles.decorativeCircle, styles.circle1]} />
      <View style={[styles.decorativeCircle, styles.circle2]} />
      <View style={[styles.decorativeCircle, styles.circle3]} />

      <View style={styles.formSection}>
        <View style={styles.headerSection}>
          <Icon
            name="lock"
            size={32}
            color="#FF6B6B"
            style={styles.headerIcon}
          />
          <Text style={styles.sectionTitle}>Create Password</Text>
          <Text style={styles.sectionSubtitle}>
            Secure your account with a strong password
          </Text>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>PASSWORD</Text>
          <View style={styles.inputWrapper}>
            <CommonTextInput
              value={passwordData.password}
              onChangeText={handlePasswordChange}
              placeholder="Enter your password"
              style={[
                styles.textInput,
                errors.password && styles.inputError,
                passwordData.password &&
                  !errors.password &&
                  styles.inputSuccess,
              ]}
              placeholderTextColor="rgba(255,255,255,0.5)"
              secureTextEntry={!showPassword}
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() => setShowPassword(!showPassword)}>
              <Icon
                name={showPassword ? 'eye-off' : 'eye'}
                size={20}
                color="rgba(255,255,255,0.6)"
              />
            </TouchableOpacity>
          </View>

          {passwordData.password && (
            <View style={styles.strengthContainer}>
              <View style={styles.strengthBar}>
                <View
                  style={[
                    styles.strengthFill,
                    {
                      width: `${passwordStrength.strength}%`,
                      backgroundColor: passwordStrength.color,
                    },
                  ]}
                />
              </View>
              <Text
                style={[styles.strengthText, {color: passwordStrength.color}]}>
                {passwordStrength.text}
              </Text>
            </View>
          )}

          {errors.password && (
            <View style={styles.errorContainer}>
              <Icon name="alert-circle" size={16} color="#FF6B6B" />
              <Text style={styles.errorText}>{errors.password}</Text>
            </View>
          )}
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>CONFIRM PASSWORD</Text>
          <View style={styles.inputWrapper}>
            <CommonTextInput
              value={passwordData.confirmPassword}
              onChangeText={handleConfirmPasswordChange}
              placeholder="Confirm your password"
              style={[
                styles.textInput,
                errors.confirmPassword && styles.inputError,
                passwordData.confirmPassword &&
                  passwordData.password === passwordData.confirmPassword &&
                  styles.inputSuccess,
              ]}
              placeholderTextColor="rgba(255,255,255,0.5)"
              secureTextEntry={!showConfirmPassword}
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() => setShowConfirmPassword(!showConfirmPassword)}>
              <Icon
                name={showConfirmPassword ? 'eye-off' : 'eye'}
                size={20}
                color="rgba(255,255,255,0.6)"
              />
            </TouchableOpacity>
          </View>

          {passwordData.confirmPassword &&
            passwordData.password === passwordData.confirmPassword && (
              <View style={styles.successContainer}>
                <Icon name="check-circle" size={16} color="#4CAF50" />
                <Text style={styles.successText}>Passwords match!</Text>
              </View>
            )}

          {errors.confirmPassword && (
            <View style={styles.errorContainer}>
              <Icon name="alert-circle" size={16} color="#FF6B6B" />
              <Text style={styles.errorText}>{errors.confirmPassword}</Text>
            </View>
          )}
        </View>

        <View style={styles.requirementsContainer}>
          <Text style={styles.requirementsTitle}>Password Requirements:</Text>
          <View style={styles.requirementsList}>
            {[
              {
                text: 'At least 8 characters',
                check: passwordData.password.length >= 8,
              },
              {
                text: 'One uppercase letter',
                check: /[A-Z]/.test(passwordData.password),
              },
              {
                text: 'One lowercase letter',
                check: /[a-z]/.test(passwordData.password),
              },
              {text: 'One number', check: /\d/.test(passwordData.password)},
              {
                text: 'One special character',
                check: /[!@#$%^&*(),.?":{}|<>]/.test(passwordData.password),
              },
            ].map((req, index) => (
              <View key={index} style={styles.requirement}>
                <Icon
                  name={req.check ? 'check-circle' : 'circle'}
                  size={14}
                  color={req.check ? '#4CAF50' : 'rgba(255,255,255,0.3)'}
                />
                <Text
                  style={[
                    styles.requirementText,
                    req.check && styles.requirementMet,
                  ]}>
                  {req.text}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </View>

      <View style={styles.bottomContainer}>
        <TouchableOpacity
          style={[styles.continueButton, isContinuing && styles.buttonDisabled]}
          onPress={handleContinue}
          disabled={isContinuing}>
          <LinearGradient
            colors={isContinuing ? ['#666', '#666'] : ['#FF6B6B', '#FF8A80']}
            style={styles.buttonGradient}
            start={{x: 0, y: 0}}
            end={{x: 1, y: 0}}>
            <Icon
              name="user-plus"
              size={20}
              color="#fff"
              style={styles.buttonIcon}
            />
            <Text style={styles.buttonText}>
              {isContinuing ? 'Processing...' : 'Continue'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  decorativeCircle: {
    position: 'absolute',
    borderRadius: 999,
    opacity: 0.1,
  },
  circle1: {
    width: 200,
    height: 200,
    backgroundColor: '#FF6B6B',
    top: -100,
    right: -100,
  },
  circle2: {
    width: 150,
    height: 150,
    backgroundColor: '#4ECDC4',
    top: '30%',
    left: -75,
  },
  circle3: {
    width: 100,
    height: 100,
    backgroundColor: '#FFE66D',
    bottom: '20%',
    right: -50,
  },
  formSection: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    zIndex: 1,
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  headerIcon: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#FFFFFF',
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.7)',
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: 'rgba(255,255,255,0.8)',
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
    letterSpacing: 1,
  },
  inputWrapper: {
    position: 'relative',
  },
  textInput: {
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.2)',
    borderRadius: 12,
    fontSize: 16,
    fontFamily: Fonts.ROBO_REGULAR,
    backgroundColor: 'rgba(255,255,255,0.1)',
    color: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 14,
    paddingRight: 50,
  },
  eyeIcon: {
    position: 'absolute',
    right: 16,
    top: '50%',
    transform: [{translateY: -10}],
  },
  inputError: {
    borderColor: '#FF6B6B',
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  inputSuccess: {
    borderColor: '#4CAF50',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
  },
  strengthContainer: {
    marginTop: 8,
  },
  strengthBar: {
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  strengthFill: {
    height: '100%',
    borderRadius: 2,
  },
  strengthText: {
    fontSize: 12,
    fontFamily: Fonts.ROBO_REGULAR,
    marginTop: 4,
    textAlign: 'right',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: 14,
    marginLeft: 6,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  successText: {
    color: '#4CAF50',
    fontSize: 14,
    marginLeft: 6,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  requirementsContainer: {
    marginTop: 8,
    padding: 16,
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  requirementsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: 'rgba(255,255,255,0.8)',
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 12,
  },
  requirementsList: {
    gap: 8,
  },
  requirement: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  requirementText: {
    fontSize: 13,
    color: 'rgba(255,255,255,0.6)',
    fontFamily: Fonts.ROBO_REGULAR,
    marginLeft: 8,
  },
  requirementMet: {
    color: '#4CAF50',
  },
  bottomContainer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
    zIndex: 1,
  },
  continueButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#FF6B6B',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
});

export default RegistrationPasswordScreen;
 */
