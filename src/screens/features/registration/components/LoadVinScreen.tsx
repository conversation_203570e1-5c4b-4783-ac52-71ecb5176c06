import React, {useState} from 'react';
import {View, Text, StyleSheet, Alert} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import VinDetailsComponent from './VinDetailsComponent';
import {
  AppStrings,
  ExceptionStrings,
} from '../../../../utils/constants/AppStrings';
import {Colors, Fonts, Sizes} from '../../../../utils/constants/Theme';
import {VehicleService} from '../../../../utils/services/VehicleService';
import {useAuth} from '../../../../utils/configs/AuthContext';
import {VIN_URL} from '../../../../constants/constants';

interface VehicleInfo {
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
  warning: string;
  error: string;
  valid: boolean;
}

interface LoadVinScreenProps {
  vinData: {vinNumber: string; vehicleInfo: VehicleInfo};
  userData: {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
    password: string;
  };
  setVinData: (data: {vinNumber: string; vehicleInfo: VehicleInfo}) => void;
  onContinue: () => void;
}

const LoadVinScreen: React.FC<LoadVinScreenProps> = ({
  userData,
  vinData,
  setVinData,
  onContinue,
}) => {
  const {updateRegistrationStatus, signUp, updateUserRegistration} = useAuth();
  const [vinNumber, setVinNumber] = useState(vinData.vinNumber || '');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [userId, setUserId] = useState('');

  const searchVIN = async (vin: string) => {
    const FORMAT = '?format=json';
    try {
      const response = await fetch(VIN_URL + vin + FORMAT);
      const data = await response.json();
      return data;
    } catch (error) {
      Alert.alert('Error', 'Failed to fetch VIN data');
      return null;
    }
  };

  const processVIN = (vinApiData: any, vin: string) => {
    let vinInfo = {
      vin: vin,
      make: '',
      model: '',
      manufactureYear: '',
      fuel: '',
      warning: '',
      error: '',
      valid: false,
    };

    for (let vinIndex = 0; vinIndex < vinApiData.Count; vinIndex++) {
      switch (vinApiData.Results[vinIndex].VariableId) {
        case 26:
          vinInfo.make = vinApiData.Results[vinIndex].Value;
          break;
        case 28:
          vinInfo.model = vinApiData.Results[vinIndex].Value;
          break;
        case 29:
          vinInfo.manufactureYear = vinApiData.Results[vinIndex].Value;
          break;
        case 24:
          vinInfo.fuel = vinApiData.Results[vinIndex].Value;
          break;
        case 143:
          vinInfo.warning = vinApiData.Results[vinIndex].Value;
          break;
        case 156:
          vinInfo.error = vinApiData.Results[vinIndex].Value
            ? 'The Model Year decoded for this VIN may be incorrect'
            : '';
          break;
        default:
          break;
      }
    }

    if (!vinInfo.error) {
      if (!vinInfo.make) {
        vinInfo.error = 'The Model Year decoded for this VIN may be incorrect';
        vinInfo.valid = false;
        return vinInfo;
      }
      vinInfo.valid = true;
      return vinInfo;
    } else {
      vinInfo.valid = false;
      return vinInfo;
    }
  };

  const loadVIN = async () => {
    if (!vinNumber.trim() || vinNumber.length !== 17) {
      Alert.alert(
        ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL,
        ExceptionStrings.MCX_EXCEPTION_ENTER_VALID_VIN_LABEL,
      );
      return;
    }
    setLoading(true);
    const vinApiData = await searchVIN(vinNumber);
    if (vinApiData) {
      const processedVin = processVIN(vinApiData, vinNumber);
      setVinData({
        vinNumber,
        vehicleInfo: processedVin,
      });
      if (!processedVin.valid) {
        Alert.alert('VIN Error', processedVin.error || 'Invalid VIN data');
      } else {
        Alert.alert('Success', 'VIN loaded successfully');
      }
    }
    setLoading(false);
  };

  const handleLogin = async () => {
    setSaving(true);
    try {
      if (!userData) {
        throw new Error(
          'User Data not found. Please start registration from step 1.',
        );
      }
      const user = await signUp(userData.email, userData.password);
      if (!user.uid) {
        throw new Error(
          'User ID not found. Please start registration from step 1.',
        );
      }
      setUserId(user.uid);
      await updateUserRegistration(
        user.uid,
        userData.firstName,
        userData.lastName,
        userData.email,
        userData.mobile,
        'email',
        user.uid,
        'step3_completed',
      );
      await AsyncStorage.setItem('userId', user.uid);
      await AsyncStorage.setItem('userEmail', userData.email);
      await updateRegistrationStatus(user.uid, 'registration_completed');

      Alert.alert('Success', 'Vehicle information saved successfully!', [
        {text: 'OK', onPress: () => onContinue()},
      ]);
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.message || 'Failed to save vehicle information',
      );
    } finally {
      setSaving(false);
    }
  };

  const handleContinueWithVehicle = async () => {
    if (!vinData.vehicleInfo.valid) {
      Alert.alert(
        ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL,
        ExceptionStrings.MCX_EXCEPTION_ENTER_VALID_VIN_LABEL,
      );
      return;
    }
    await handleLogin();
    await VehicleService.addVehicleInfo(userId, {
      vin: vinData.vehicleInfo.vin,
      make: vinData.vehicleInfo.make,
      model: vinData.vehicleInfo.model,
      manufactureYear: vinData.vehicleInfo.manufactureYear,
      fuel: vinData.vehicleInfo.fuel,
      warning: vinData.vehicleInfo.warning,
    });
  };

  const handleSkipVehicle = async () => {
    setSaving(true);
    await handleLogin();
    try {
      if (userId) {
        await updateRegistrationStatus(
          userId,
          'registration_completed_no_vehicle',
        );
      }
      onContinue();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to complete registration');
    } finally {
      setSaving(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.vehicleSection}>
        <RegistrationTitleSection
          title={AppStrings.MCX_MY_VEHICLE_TITLE}
          backgroundColor="#FFFFFF"
          borderBottomWidth={1}
          borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
          paddingVertical={16}
          paddingHorizontal={0}
        />
        <View style={styles.inputMainContainer}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{AppStrings.MCX_VIN_LABEL}</Text>
            <CommonTextInput
              value={vinNumber}
              onChangeText={setVinNumber}
              placeholder="Enter VIN number"
              style={styles.vinInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
              maxLength={17}
            />
          </View>
          <CustomButton
            text={loading ? 'Loading...' : AppStrings.MCX_LOAD_VIN_BUTTON}
            onPress={loadVIN}
            variant="primary"
            size="small"
            backgroundColor={Colors.PRIMARY}
            textColor="#fff"
            style={styles.loadVinButton}
            isBoldText={true}
            disabled={loading}
          />
        </View>
        {vinData.vehicleInfo.valid && (
          <VinDetailsComponent
            warning={vinData.vehicleInfo.warning}
            vin={vinData.vehicleInfo.vin}
            make={vinData.vehicleInfo.make}
            model={vinData.vehicleInfo.model}
            manufactureYear={vinData.vehicleInfo.manufactureYear}
            fuel={vinData.vehicleInfo.fuel}
          />
        )}
      </View>
      <View style={styles.bottomContainer}>
        <View style={styles.buttonContainer}>
          <CustomButton
            text={saving ? 'Saving...' : 'Skip Now'}
            onPress={handleSkipVehicle}
            variant="outline"
            size="large"
            backgroundColor="#FFFFFF"
            textColor={Colors.SECONDARY}
            isBoldText={true}
            style={styles.skipButton}
            disabled={loading || saving}
          />
          <CustomButton
            text={saving ? 'Saving...' : AppStrings.MCX_CONTINUE_BUTTON}
            onPress={handleContinueWithVehicle}
            variant="primary"
            size="large"
            fullWidth={true}
            backgroundColor={Colors.SECONDARY}
            textColor="#fff"
            isBoldText={true}
            isBottomButton={true}
            bottomLineWidth={1}
            bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
            disabled={loading || saving || !vinData.vehicleInfo.valid}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
    justifyContent: 'flex-start',
  },
  buttonContainer: {
    flexDirection: 'column',
    gap: 10,
    paddingHorizontal: 12,
  },
  skipButton: {
    borderWidth: 1,
    borderColor: Colors.SECONDARY,
    marginBottom: 10,
  },
  vehicleSection: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    marginHorizontal: 12,
    borderRadius: 2,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
  },
  inputContainer: {
    marginBottom: 20,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  inputLabel: {
    fontSize: Sizes.MEDIUM,
    fontWeight: '500',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
  },
  vinInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    backgroundColor: '#FFFFFF',
  },
  loadVinButton: {
    borderRadius: 2,
    paddingVertical: 10,
    minHeight: 'auto',
    marginTop: 14,
    marginBottom: 10,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  inputMainContainer: {
    paddingHorizontal: 0,
  },
});

export default LoadVinScreen;
/* import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  Animated,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import VinDetailsComponent from './VinDetailsComponent';
import {
  AppStrings,
  ExceptionStrings,
} from '../../../../utils/constants/AppStrings';
import {Colors, Fonts, Sizes} from '../../../../utils/constants/Theme';
import {VehicleService} from '../../../../utils/services/VehicleService';
import {useAuth} from '../../../../utils/configs/AuthContext';
import {VIN_URL} from '../../../../constants/constants';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

interface VehicleInfo {
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
  warning: string;
  error: string;
  valid: boolean;
}

interface LoadVinScreenProps {
  vinData: {vinNumber: string; vehicleInfo: VehicleInfo};
  userData: {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
    password: string;
  };
  setVinData: (data: {vinNumber: string; vehicleInfo: VehicleInfo}) => void;
  onContinue: () => void;
}

const LoadVinScreen: React.FC<LoadVinScreenProps> = ({
  userData,
  vinData,
  setVinData,
  onContinue,
}) => {
  const {updateRegistrationStatus, signUp, updateUserRegistration} = useAuth();
  const [vinNumber, setVinNumber] = useState(vinData.vinNumber || '');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [userId, setUserId] = useState('');

  const [fadeAnim] = React.useState(new Animated.Value(0));

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  const searchVIN = async (vin: string) => {
    const FORMAT = '?format=json';
    try {
      const response = await fetch(VIN_URL + vin + FORMAT);
      const data = await response.json();
      return data;
    } catch (error) {
      Alert.alert('Error', 'Failed to fetch VIN data');
      return null;
    }
  };

  const processVIN = (vinApiData: any, vin: string) => {
    let vinInfo = {
      vin: vin,
      make: '',
      model: '',
      manufactureYear: '',
      fuel: '',
      warning: '',
      error: '',
      valid: false,
    };

    for (let vinIndex = 0; vinIndex < vinApiData.Count; vinIndex++) {
      switch (vinApiData.Results[vinIndex].VariableId) {
        case 26:
          vinInfo.make = vinApiData.Results[vinIndex].Value;
          break;
        case 28:
          vinInfo.model = vinApiData.Results[vinIndex].Value;
          break;
        case 29:
          vinInfo.manufactureYear = vinApiData.Results[vinIndex].Value;
          break;
        case 24:
          vinInfo.fuel = vinApiData.Results[vinIndex].Value;
          break;
        case 143:
          vinInfo.warning = vinApiData.Results[vinIndex].Value;
          break;
        case 156:
          vinInfo.error = vinApiData.Results[vinIndex].Value
            ? 'The Model Year decoded for this VIN may be incorrect'
            : '';
          break;
        default:
          break;
      }
    }

    if (!vinInfo.error) {
      if (!vinInfo.make) {
        vinInfo.error = 'The Model Year decoded for this VIN may be incorrect';
        vinInfo.valid = false;
        return vinInfo;
      }
      vinInfo.valid = true;
      return vinInfo;
    } else {
      vinInfo.valid = false;
      return vinInfo;
    }
  };

  const loadVIN = async () => {
    if (!vinNumber.trim() || vinNumber.length !== 17) {
      Alert.alert(
        ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL,
        ExceptionStrings.MCX_EXCEPTION_ENTER_VALID_VIN_LABEL,
      );
      return;
    }
    setLoading(true);
    const vinApiData = await searchVIN(vinNumber);
    if (vinApiData) {
      const processedVin = processVIN(vinApiData, vinNumber);
      setVinData({
        vinNumber,
        vehicleInfo: processedVin,
      });
      if (!processedVin.valid) {
        Alert.alert('VIN Error', processedVin.error || 'Invalid VIN data');
      } else {
        Alert.alert('Success', 'VIN loaded successfully');
      }
    }
    setLoading(false);
  };

  const handleLogin = async () => {
    setSaving(true);
    try {
      if (!userData) {
        throw new Error(
          'User Data not found. Please start registration from step 1.',
        );
      }
      const user = await signUp(userData.email, userData.password);
      if (!user.uid) {
        throw new Error(
          'User ID not found. Please start registration from step 1.',
        );
      }
      setUserId(user.uid);
      await updateUserRegistration(
        user.uid,
        userData.firstName,
        userData.lastName,
        userData.email,
        userData.mobile,
        'email',
        user.uid,
        'step3_completed',
      );
      await AsyncStorage.setItem('userId', user.uid);
      await AsyncStorage.setItem('userEmail', userData.email);
      await updateRegistrationStatus(user.uid, 'registration_completed');

      Alert.alert('Success', 'Vehicle information saved successfully!', [
        {text: 'OK', onPress: () => onContinue()},
      ]);
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.message || 'Failed to save vehicle information',
      );
    } finally {
      setSaving(false);
    }
  };

  const handleContinueWithVehicle = async () => {
    if (!vinData.vehicleInfo.valid) {
      Alert.alert(
        ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL,
        ExceptionStrings.MCX_EXCEPTION_ENTER_VALID_VIN_LABEL,
      );
      return;
    }
    await handleLogin();
    await VehicleService.addVehicleInfo(userId, {
      vin: vinData.vehicleInfo.vin,
      make: vinData.vehicleInfo.make,
      model: vinData.vehicleInfo.model,
      manufactureYear: vinData.vehicleInfo.manufactureYear,
      fuel: vinData.vehicleInfo.fuel,
      warning: vinData.vehicleInfo.warning,
    });
  };

  const handleSkipVehicle = async () => {
    setSaving(true);
    await handleLogin();
    try {
      if (userId) {
        await updateRegistrationStatus(
          userId,
          'registration_completed_no_vehicle',
        );
      }
      onContinue();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to complete registration');
    } finally {
      setSaving(false);
    }
  };

  return (
    <Animated.View style={[styles.vinScreenContainer, {opacity: fadeAnim}]}>
      <View style={styles.headerSection}>
        <Icon
          name="car-search"
          size={32}
          color="#ff6b6b"
          style={styles.headerIcon}
        />
        <Text style={styles.sectionTitle}>Vehicle Details</Text>
        <Text style={styles.sectionSubtitle}>
          Enter your vehicle's VIN to get started
        </Text>
      </View>

      <View style={styles.vinInputSection}>
        <Text style={styles.inputLabel}>VIN Number</Text>
        <View style={styles.vinInputWrapper}>
          <Icon
            name="barcode-scan"
            size={20}
            color="#a0a0a0"
            style={styles.inputIcon}
          />
          <TextInput
            style={styles.vinInput}
            value={vinNumber}
            onChangeText={setVinNumber}
            placeholder="Enter 17-character VIN"
            placeholderTextColor="#666"
            maxLength={17}
            autoCapitalize="characters"
            autoCorrect={false}
          />
        </View>

        <TouchableOpacity
          style={styles.loadVinBtn}
          onPress={() => {
          }}
          disabled={loading}
          activeOpacity={0.8}>
          <LinearGradient
            colors={loading ? ['#999', '#666'] : ['#74b9ff', '#0984e3']}
            style={styles.loadButtonGradient}>
            <Icon
              name={loading ? 'loading' : 'magnify'}
              size={18}
              color="white"
              style={[styles.buttonIcon, loading && styles.rotatingIcon]}
            />
            <Text style={styles.loadButtonText}>
              {loading ? 'Loading...' : 'Load VIN'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>

      {vinData.vehicleInfo.valid && (
        <VinDetailsComponent
          warning={vinData.vehicleInfo.warning}
          vin={vinData.vehicleInfo.vin}
          make={vinData.vehicleInfo.make}
          model={vinData.vehicleInfo.model}
          manufactureYear={vinData.vehicleInfo.manufactureYear}
          fuel={vinData.vehicleInfo.fuel}
        />
      )}

      <View style={styles.vinBottomContainer}>
        <TouchableOpacity
          style={styles.skipBtn}
          onPress={() => {
          }}
          disabled={loading || saving}
          activeOpacity={0.8}>
          <Text style={styles.skipBtnText}>Skip for Now</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.continueBtn,
            (!vinData.vehicleInfo.valid || saving) && styles.disabledBtn,
          ]}
          onPress={() => {
          }}
          disabled={loading || saving || !vinData.vehicleInfo.valid}
          activeOpacity={0.8}>
          <LinearGradient
            colors={saving ? ['#999', '#666'] : ['#ff6b6b', '#ee5a24']}
            style={styles.buttonGradient}>
            <Text style={styles.buttonText}>
              {saving ? 'Saving...' : 'Continue'}
            </Text>
            {!saving && (
              <Icon
                name="arrow-right"
                size={20}
                color="white"
                style={styles.buttonIcon}
              />
            )}
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  // Common Styles
  container: {
    flex: 1,
  },
  animatedContainer: {
    flex: 1,
  },
  headerSection: {
    alignItems: 'center',
    paddingVertical: 24,
    paddingHorizontal: 24,
  },
  headerIcon: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    fontFamily: Fonts.ROBO_REGULAR,
  },

  // Personal Details Styles
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: 120,
  },
  formSection: {
    paddingHorizontal: 24,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    color: 'white',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: '600',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    height: 56,
  },
  inputWrapperFocused: {
    borderColor: '#ff6b6b',
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  inputWrapperError: {
    borderColor: '#ff6b6b',
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  inputIcon: {
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    color: 'white',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
    paddingVertical: 0,
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 14,
    marginTop: 8,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 24,
    paddingBottom: 30,
  },
  continueBtn: {
    borderRadius: 16,
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 16,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  buttonIcon: {
    marginLeft: 12,
  },

  // VIN Details Styles
  vinContainer: {
    marginHorizontal: 24,
    marginTop: 16,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 193, 7, 0.1)',
    borderColor: 'rgba(255, 193, 7, 0.3)',
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  warningIcon: {
    marginRight: 12,
  },
  warningText: {
    flex: 1,
    color: '#ffc107',
    fontSize: 14,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  vinDetailsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  vinCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  vinCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginLeft: 12,
    fontFamily: Fonts.ROBO_BOLD,
  },
  vinDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  vinDetailLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  vinDetailIcon: {
    marginRight: 8,
  },
  vinDetailLabel: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  vinDetailValue: {
    fontSize: 16,
    color: 'white',
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: '600',
  },

  // VIN Screen Styles
  vinScreenContainer: {
    flex: 1,
  },
  vinInputSection: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  vinInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    height: 56,
    marginBottom: 16,
  },
  vinInput: {
    flex: 1,
    color: 'white',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
    paddingVertical: 0,
  },
  loadVinBtn: {
    borderRadius: 12,
  },
  loadButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  loadButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  rotatingIcon: {
    // Add rotation animation if needed
  },
  vinBottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 24,
    paddingBottom: 30,
    gap: 12,
  },
  skipBtn: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  skipBtnText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  disabledBtn: {
    opacity: 0.5,
  },

  // Success Screen Styles
  successContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  successContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successIconContainer: {
    marginBottom: 32,
  },
  successIconGradient: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successTitle: {
    fontSize: 32,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
  },
  successSubtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    marginBottom: 40,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  featuresContainer: {
    width: '100%',
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    textAlign: 'center',
    marginBottom: 24,
    fontFamily: Fonts.ROBO_BOLD,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    marginBottom: 12,
  },
  featureIcon: {
    marginRight: 16,
  },
  featureText: {
    flex: 1,
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  getStartedBtn: {
    borderRadius: 16,
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
});

export default LoadVinScreen;
 */
