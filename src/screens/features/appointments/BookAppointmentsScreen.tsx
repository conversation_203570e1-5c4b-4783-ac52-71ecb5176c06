import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  Alert,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { launchImageLibrary } from 'react-native-image-picker';
import Geolocation from 'react-native-geolocation-service';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import { Colors } from '../../../utils/constants/Theme';
import { AppCommonIcons, AppStrings } from '../../../utils/constants/AppStrings';
import { MechanicItem } from '../../../utils/configs/types';
import CommonDropdown from '../../../components/common/CommonDropdown';
import { VehicleService } from '../../../utils/services/VehicleService';
import { useAuth } from '../../../utils/configs/AuthContext';
import axios from 'axios';
import { GooglePlacesAutocompleteDefaultProps } from '../../../utils/configs/GooglePlacesAutocompleteProps';
import Config from 'react-native-config';
import { get } from '@react-native-firebase/database';

const validationSchema = yup.object().shape({
  yourvehicle: yup.string().required('Please select your vehicle'),
  requestlocation: yup.string().required('Please select a location type'),
  locationInput: yup.mixed().when('requestlocation', ([requestlocation], schema) => {
    return requestlocation === 'searchlocation'
      ? schema.required('Please select a location')
      : schema;
  }),
  serviceNeed: yup
    .array()
    .of(
      yup.object().shape({
        vehicleservice: yup.string().required('Please select a service'),
        subServiceNeed: yup.string().required('Please select a sub-service'),
      })
    )
    .min(1, 'At least one service is required'),
  alertconfirmation: yup.string().required('Please select alert confirmation'),
  scheduledate: yup.date().required('Please select a date'),
  notes: yup.string(),
});

const BookAppointmentsScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  const mechanic = (route.params as { mechanic: MechanicItem })?.mechanic;
  const googlePlacesRef = useRef<any>(null);

  const getFullName = () => {
    if (mechanic?.['first-name'] && mechanic?.['last-name']) {
      return `${mechanic['first-name']} ${mechanic['last-name']}`;
    }
    return mechanic?.name || 'Unknown Mechanic';
  };

  const getLocationString = () => {
    if (!mechanic) return 'Location not available';

    const parts = [];
    if (mechanic.address1) parts.push(mechanic.address1);
    if (mechanic.address2) parts.push(mechanic.address2);
    if (mechanic.city) parts.push(mechanic.city);
    if (mechanic.state) parts.push(mechanic.state);
    if (mechanic.country) parts.push(mechanic.country);
    if (mechanic.zipcode) parts.push(mechanic.zipcode);

    if (parts.length > 0) {
      return parts.join(', ');
    }

    if (mechanic.locationDetails && mechanic.locationDetails.length > 0) {
      return mechanic.locationDetails.join(', ');
    }

    return 'Location not available';
  };

  const getStarRating = () => {
    const rating = mechanic?.['mechanic-rating'] || 3;
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    let stars = '★'.repeat(fullStars);
    if (hasHalfStar) {
      stars += '☆';
    }

    return stars;
  };

  const [screeningErrors, setScreeningErrors] = useState<Set<string>>(new Set());

  const { control, watch, setValue, formState: { errors }, trigger } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      yourvehicle: '',
      requestlocation: 'currentlocation',
      locationInput: {},
      serviceNeed: [{ vehicleservice: '', subServiceNeed: '' }],
      alertconfirmation: '',
      scheduledate: new Date(),
      notes: '',
    },
    mode: 'onChange',
  });

  const { fields: serviceFields, append: appendService, remove: removeService } = useFieldArray({
    control,
    name: 'serviceNeed',
  });

  const [userVehicleList, setUserVehicleList] = useState<any[]>([]);
  const [customerRequestAdrress, setCustomerRequestAdrress] = useState<any>(null);
  const [_selectedLocation, _setSelectedLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [selectedImages, setSelectedImages] = useState<any[]>([]);

  const handleDeleteImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, idx) => idx !== index));
  };
  const [savedLocations, setSavedLocations] = useState<any[]>([]);
  const [_isLoading, _setIsLoading] = useState(false);
  const [_showDatePicker, _setShowDatePicker] = useState(false);
  const [screeningAnswers, setScreeningAnswers] = useState<Record<string, string | null>>({});
  const [_alertConfirmation, _setAlertConfirmation] = useState('');
  const [date, setDate] = useState('');
  const [notes, setNotes] = useState('');
  const [selectedSavedLocation, setSelectedSavedLocation] = useState('');
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);
  const [datePickerValue, setDatePickerValue] = useState<Date>(new Date());
  const [screeningQuestions, setScreeningQuestions] = useState<any>({});
  const [alertConfirmationData, setAlertConfirmationData] = useState<any[]>([]);
  const [serviceIdNameMap, setServiceIdNameMap] = useState<Record<string, string>>({});
  const [subServiceIdNameMap, setSubServiceIdNameMap] = useState<Record<string, string>>({});
  const [subServicePriceData, setSubServicePriceData] = useState<Record<string, any>>({});
  const [bidRates, setBidRates] = useState<Record<number, string>>({});

  const fetchSavedLocations = useCallback(async () => {
    try {
      if (user?.uid) {
        const locations = await VehicleService.fetchSavedLocations(user.uid);
        setSavedLocations(locations);
      }
    } catch (error) {
      console.error('Error fetching saved locations:', error);
      setSavedLocations([]);
    }
  }, [user?.uid]);

  useEffect(() => {
    const fetchScreeningQuestions = async () => {
      try {
        const snapshot = await get(VehicleService.getPreScreeningQuestions());
        const screeningData = snapshot.val();
        if (screeningData) {
          setScreeningQuestions(screeningData);
          const initialAnswers: Record<string, string | null> = {};
          Object.keys(screeningData).forEach(key => {
            if (screeningData[key] && !screeningData[key].hide) {
              initialAnswers[key] = null;
            }
          });
          setScreeningAnswers(initialAnswers);
        } else {
          setScreeningQuestions({});
        }
      } catch (error) {
        console.error('Error fetching screening questions:', error);
        setScreeningQuestions({});
      }
    };
    const fetchAlertConfirmations = async () => {
      try {
        const data = await VehicleService.getAlertConfirmation();
        setAlertConfirmationData(data);
      } catch (error) {
        console.error('Error fetching alert confirmations:', error);
      }
    };
    const fetchServiceMaps = async () => {
      try {
        const serviceMap = await VehicleService.getServiceIdNameMap();
        const subServiceMap = await VehicleService.getSubServiceIdNameMap();
        setServiceIdNameMap(serviceMap);
        setSubServiceIdNameMap(subServiceMap);
      } catch (error) {
        console.error('Error fetching service maps:', error);
      }
    };
    fetchScreeningQuestions();
    fetchAlertConfirmations();
    fetchServiceMaps();
    const fetchSubServicePriceData = async () => {
      try {
        const priceData = await VehicleService.getSubServicePriceData();
        setSubServicePriceData(priceData);
      } catch (error) {
        console.error('Error fetching sub-service price data:', error);
      }
    };
    fetchSubServicePriceData();
    if (user?.uid) {
      fetchUserVehicles(user.uid);
    }
    fetchSavedLocations();
  }, [user, fetchSavedLocations]);

  useEffect(() => {
    if (selectedSavedLocation) {
      const location = savedLocations.find(l => l.id === selectedSavedLocation);
      if (location) {
        const addressParts = location.address.split(',').map((part: string) => part.trim());
        setCustomerRequestAdrress({
          address_array: {
            address1: addressParts[0] || '',
            address2: '',
            city: addressParts[1] || '',
            state: addressParts[2] || '',
            country: addressParts[3] || 'India',
            zipcode: '',
          },
          formatted_address: location.address,
          location: location.coordinates,
        });
        _setSelectedLocation(location.coordinates);
      }
    }
  }, [selectedSavedLocation, savedLocations]);

  const fetchUserVehicles = async (userId: string) => {
    try {
      if (userId) {
        const vehicles = await VehicleService.getVehicles(userId);
        setUserVehicleList(vehicles);
      }
    } catch (error) {
      console.error('Error fetching user vehicles:', error);
    }
  };

  const handleConfirmAppointment = async (data: any) => {
    const _screeningErrors = validateScreeningQuestions();
    if (_screeningErrors.size > 0) {
      Alert.alert('Incomplete Screening', 'Please complete all required screening questions before submitting. Note: Secondary contact requires a valid 10-digit phone number.');
      return;
    }
    _setIsLoading(true);
    try {
      let imageUrls: string[] = [];
      if (selectedImages.length > 0) {
        const imageUris = selectedImages.map(img => img.uri);
        imageUrls = await VehicleService.uploadMultipleImages(imageUris);
      }
      const currentTime = Math.floor(Date.now() / 1000);
      const requestDate = data.scheduledate.toISOString().split('T')[0];
      const selectedAlert = alertConfirmationData.find(alert => alert.value === data.alertconfirmation);
      let alertValue = '30';
      if (selectedAlert?.label) {
        const match = selectedAlert.label.match(/(\d+(?:\.\d+)?)\s*(hour|minute)/i);
        if (match) {
          const value = parseFloat(match[1]);
          const unit = match[2].toLowerCase();
          if (unit.includes('hour')) {
            alertValue = (value * 60).toString();
          } else {
            alertValue = value.toString();
          }
        }
      }
      const screeningFAQ: Record<string, any> = {};
      Object.keys(screeningAnswers).forEach(key => {
        if (screeningAnswers[key] && screeningQuestions[key]) {
          screeningFAQ[key] = {
            answer: screeningAnswers[key].toUpperCase(),
            questions: screeningQuestions[key].question,
          };
        }
      });
      const services: Record<string, any> = {};
      data.serviceNeed.forEach((service: any, index: number) => {
        if (service.vehicleservice && service.subServiceNeed) {
          const serviceKey = service.vehicleservice;
          const bidRate = bidRates[index] || '0';
          services[serviceKey] = {
            'customer-bid': [bidRate],
            'mechanic-bid': ['0'],
            'service-type': serviceKey,
            'sub-services': [service.subServiceNeed],
          };
        }
      });
      const workRequestData = {
        alert: alertValue,
        'created-time': currentTime,
        customer: user?.uid || '',
        latitude: customerRequestAdrress?.location?.latitude || 0,
        longitude: customerRequestAdrress?.location?.longitude || 0,
        mechanic: mechanic?.id?.toString() || '',
        notes: {
          images: imageUrls.join(','),
          'notes-content': notes || '',
        },
        'request-address': {
          address_array: {
            address1: customerRequestAdrress?.address_array?.address1 || '',
            address2: customerRequestAdrress?.address_array?.address2 || '',
            city: customerRequestAdrress?.address_array?.city || '',
            country: customerRequestAdrress?.address_array?.country || 'India',
            state: customerRequestAdrress?.address_array?.state || '',
            zipcode: customerRequestAdrress?.address_array?.zipcode || '',
          },
          formatted_address: customerRequestAdrress?.formatted_address || '',
          location: {
            latitude: customerRequestAdrress?.location?.latitude || 0,
            longitude: customerRequestAdrress?.location?.longitude || 0,
          },
        },
        'request-date': requestDate,
        'request-time-range': '1:00 pm - 4:30 pm',
        'request-type': 'Appointment',
        'screening-FAQ': screeningFAQ,
        services: services,
        status: 'Pending',
        vehicle: data.yourvehicle || '',
      };
      const workRequestId = await VehicleService.createWorkRequest(workRequestData);
      console.log('Work request created with ID:', workRequestId);
      Alert.alert('Success', 'Appointment confirmed and work request created!');
      navigation.navigate('DashBoard' as never);
    } catch (error) {
      console.error('Error submitting appointment:', error);
      Alert.alert('Error', 'Failed to submit appointment. Please try again.');
    } finally {
      _setIsLoading(false);
    }
  };

  const validateScreeningQuestions = () => {
    if (!screeningQuestions || Object.keys(screeningQuestions).length === 0) {
      return new Set<string>();
    }
    const requiredKeys = Object.keys(screeningQuestions).filter(
      (key) => screeningQuestions[key] && !screeningQuestions[key].hide && key !== 'que-6'
    );
    const newScreeningErrors = new Set<string>();
    requiredKeys.forEach((key) => {
      const answer = screeningAnswers[key];
      if (answer === null || answer === undefined || answer.trim() === '') {
        newScreeningErrors.add(key);
        return;
      }
      if (key === 'que-4') {
        const phoneNumber = answer.trim();
        if (phoneNumber.length < 10 || !/^\d{10}$/.test(phoneNumber)) {
          newScreeningErrors.add(key);
        }
      }
    });
    return newScreeningErrors;
  };

  const handleSubmitWithValidation = async () => {
    const currentValues = watch();
    let locationError = false;
    if (currentValues.requestlocation === 'currentlocation' && !customerRequestAdrress) {
      locationError = true;
      Alert.alert('Location Required', 'Please wait for current location to load or select a different location type.');
      return;
    } else if (currentValues.requestlocation === 'savedlocation' && !selectedSavedLocation) {
      locationError = true;
      Alert.alert('Location Required', 'Please select a saved location.');
      return;
    } else if (currentValues.requestlocation === 'searchlocation' && !customerRequestAdrress) {
      locationError = true;
      Alert.alert('Location Required', 'Please search and select a location.');
      return;
    }

    const isValid = await trigger();
    console.log('Form is valid:', isValid);

    if (!isValid || locationError) {
      return;
    }

    const _screeningErrors = validateScreeningQuestions();
    setScreeningErrors(_screeningErrors);
    if (_screeningErrors.size > 0) {
      Alert.alert('Incomplete Screening', 'Please complete all required screening questions before submitting. Note: Secondary contact requires a valid 10-digit phone number.');
      return;
    }
    await handleConfirmAppointment(currentValues);
  };

  const handleCancel = () => {
    Alert.alert(
      'Confirm',
      'Are you sure you want to leave this screen? All entered data will be lost.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'OK', onPress: () => navigation.goBack() },
      ]
    );
  };

  const handleLocationTypeChange = (type: string) => {
    setValue('requestlocation', type);
    if (type === 'currentlocation') {
      requestCurrentLocation();
    } else if (type === 'savedlocation') {
      requestSavedLocation();
    } else {
      setCustomerRequestAdrress(null);
      _setSelectedLocation(null);
      if (googlePlacesRef.current) {
        googlePlacesRef.current.setAddressText('');
      }
    }
  };

  const processLocation = useCallback(async (latitude: number, longitude: number) => {
    try {
      const response = await axios.get(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${Config.GOOGLE_PLACES_API_KEY}`
      );

      if (response.data.results.length > 0) {
        const result = response.data.results[0];
        const parsedAddress = parseAddressComponents(
          result.address_components || [],
          result.formatted_address || ''
        );

        const locationData = {
          address_array: parsedAddress,
          formatted_address: result.formatted_address,
          location: { latitude, longitude },
        };

        setCustomerRequestAdrress(locationData);
        setValue('locationInput', {
          formatted_address: result.formatted_address,
          geometry: { location: { lat: latitude, lng: longitude } },
          address_components: result.address_components || [],
        });
        _setSelectedLocation({ latitude, longitude });
      }
    } catch (error) {
      console.error('Error in processLocation:', error);
    }
  }, [setValue]);

  const requestCurrentLocation = useCallback(async () => {
    try {
      const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION);
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        Geolocation.getCurrentPosition(
          async (position) => {
            const { latitude, longitude } = position.coords;
            if (user?.uid) {
              try {
                await VehicleService.updateCustomerLocation(user.uid, { latitude, longitude });
              } catch (error) {
                console.error('Error updating location in DB:', error);
              }
            }
            await processLocation(latitude, longitude);
          },
          (error) => {
            console.error('Geolocation error:', error);
            Alert.alert('Error', 'Unable to get current location');
          },
          { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
        );
      } else {
        Alert.alert('Permission Denied', 'Location permission is required');
      }
    } catch (error) {
      console.error('Location permission error:', error);
    }
  }, [user?.uid, processLocation]);

  useEffect(() => {
    const loadCurrentLocation = async () => {
      try {
        await requestCurrentLocation();
      } catch (error) {
        console.error('Error loading current location:', error);
      }
    };
    loadCurrentLocation();
  }, [requestCurrentLocation]);

  useEffect(() => {
    const today = new Date();
    setDatePickerValue(today);
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const year = today.getFullYear().toString();
    const formattedDate = `${month}/${day}/${year}`;
    setDate(formattedDate);
    setValue('scheduledate', today);
  }, [setValue]);

  const requestSavedLocation = () => {
    if (savedLocations.length === 0) {
      Alert.alert('No Saved Locations', "You don't have any saved locations yet.");
    }
  };

  const parseAddressComponents = (addressComponents: any[], formattedAddress: string) => {
    const addressData = {
      address1: '',
      address2: '',
      city: '',
      state: '',
      country: '',
      zipcode: '',
    };

    if (addressComponents && addressComponents.length > 0) {
      addressComponents.forEach((component: any) => {
        const types = component.types;

        if (types.includes('street_number') || types.includes('route')) {
          if (!addressData.address1) {
            addressData.address1 = component.long_name;
          } else {
            addressData.address1 += ' ' + component.long_name;
          }
        } else if (types.includes('sublocality') || types.includes('neighborhood')) {
          addressData.address2 = component.long_name;
        } else if (types.includes('locality') || types.includes('administrative_area_level_2')) {
          addressData.city = component.long_name;
        } else if (types.includes('administrative_area_level_1')) {
          addressData.state = component.long_name;
        } else if (types.includes('country')) {
          addressData.country = component.long_name;
        } else if (types.includes('postal_code')) {
          addressData.zipcode = component.long_name;
        }
      });
    }
    // Fallback: if address1 is empty, use the first part of formatted address
    if (!addressData.address1 && formattedAddress) {
      const parts = formattedAddress.split(',');
      addressData.address1 = parts[0]?.trim() || '';
      if (parts.length > 1 && !addressData.city) {
        addressData.city = parts[1]?.trim() || '';
      }
      if (parts.length > 2 && !addressData.state) {
        addressData.state = parts[2]?.trim() || '';
      }
    }

    return addressData;
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }

    if (selectedDate) {
      setDatePickerValue(selectedDate);
      const month = (selectedDate.getMonth() + 1).toString().padStart(2, '0');
      const day = selectedDate.getDate().toString().padStart(2, '0');
      const year = selectedDate.getFullYear().toString();
      const formattedDate = `${month}/${day}/${year}`;
      setDate(formattedDate);
      setValue('scheduledate', selectedDate);

      if (Platform.OS === 'ios') {
        setShowDatePicker(false);
      }
    } else if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }
  };

  const _handleImagePick = () => {
    const options = {
      mediaType: 'photo' as const,
      includeBase64: false,
      maxHeight: 2000,
      maxWidth: 2000,
    };

    launchImageLibrary(options, (response) => {
      if (response.didCancel) {
        console.log('User cancelled image picker');
      } else if (response.errorMessage) {
        console.error('ImagePicker Error: ', response.errorMessage);
      } else if (response.assets && response.assets[0]) {
        setSelectedImages([...selectedImages, response.assets[0]]);
      }
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleCancel}>
          <Image source={AppCommonIcons.MCX_ARROW_RIGHT} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{AppStrings.MCX_SCHEDULE_SERVICE_TITLE}</Text>
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{AppStrings.MCX_MY_MECHANIC_TITLE}</Text>
          <View style={styles.mechanicCard}>
            <Image
              source={mechanic?.imageUrl ? { uri: mechanic.imageUrl } : AppCommonIcons.MCX_USER_PROFILE_PIC}
              style={styles.mechanicImage}
            />
            <View style={styles.mechanicInfo}>
              <Text style={styles.mechanicName}>{getFullName()}</Text>
              <Text style={styles.mechanicLocation}>{getLocationString()}</Text>
              <View style={styles.ratingRow}>
                <Text style={styles.ratingStars}>{getStarRating()}</Text>
                <Text style={styles.availabilityText}>
                  Availability <Text style={[styles.appointmentText, { color: mechanic?.availability ? 'green' : 'red' }]}>
                    {mechanic?.availability ? 'Available' : 'Not Available'}
                  </Text>
                </Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{AppStrings.MCX_MY_VEHICLE_TEXT}</Text>
          <View style={styles.dropdownContainer}>
            <Text style={styles.dropdownLabel}>{AppStrings.MCX_MY_VEHICLE_TEXT}</Text>
            <Controller
              control={control}
              name="yourvehicle"
              render={({ field: { onChange, value } }) => (
                <CommonDropdown
                  data={userVehicleList.map((item) => ({ label: String(item.label), value: String(item.value) }))}
                  value={value}
                  onValueChange={onChange}
                  placeholder="Select my vehicle"
                />
              )}
            />
            {errors.yourvehicle && <Text style={styles.errorText}>{errors.yourvehicle.message}</Text>}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{AppStrings.MCX_MY_LOCATION_TEXT}</Text>
          <Controller
            control={control}
            name="requestlocation"
            render={({ field: { onChange, value } }) => (
              <View style={styles.radioGroup}>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => {
                    onChange('currentlocation');
                    handleLocationTypeChange('currentlocation');
                  }}
                >
                  <View style={[styles.radioCircle, value === 'currentlocation' && styles.radioCircleSelected]} />
                  <Text style={styles.optionText}>{AppStrings.MCX_CHOOSE_CURRENT_LOCATION_TEXT}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => {
                    onChange('savedlocation');
                    handleLocationTypeChange('savedlocation');
                  }}
                >
                  <View style={[styles.radioCircle, value === 'savedlocation' && styles.radioCircleSelected]} />
                  <Text style={styles.optionText}>{AppStrings.MCX_SAVED_LOCATION_TEXT}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => {
                    onChange('searchlocation');
                    handleLocationTypeChange('searchlocation');
                  }}
                >
                  <View style={[styles.radioCircle, value === 'searchlocation' && styles.radioCircleSelected]} />
                  <Text style={styles.optionText}>{AppStrings.MCX_SEARCH_NEARBY_LOCATION_TEXT}</Text>
                </TouchableOpacity>
              </View>
            )}
          />

          {watch('requestlocation') === 'searchlocation' && (
            <View style={styles.locationInputContainer}>
              <GooglePlacesAutocomplete
                {...GooglePlacesAutocompleteDefaultProps}
                ref={googlePlacesRef}
                placeholder="Search for a location"
                minLength={2}
                fetchDetails={true}
                onPress={(data, details = null) => {
                  if (details) {
                    setValue('locationInput', details);
                    const parsedAddress = parseAddressComponents(
                      details.address_components || [],
                      details.formatted_address || ''
                    );
                    setCustomerRequestAdrress({
                      address_array: parsedAddress,
                      formatted_address: details.formatted_address,
                      location: {
                        latitude: details.geometry.location.lat,
                        longitude: details.geometry.location.lng,
                      },
                    });
                    _setSelectedLocation({
                      latitude: details.geometry.location.lat,
                      longitude: details.geometry.location.lng,
                    });
                  }
                }}
                query={{
                  key: Config.GOOGLE_PLACES_API_KEY,
                  language: 'en',
                  components: 'country:in',
                }}
                debounce={300}
                styles={{
                  container: { flex: 0, zIndex: 1000 },
                  textInput: styles.locationInput,
                  listView: { zIndex: 1000 },
                }}
                enablePoweredByContainer={false}
              />
              {errors.locationInput && (
                <Text style={styles.errorText}>{String(errors.locationInput.message || 'Location is required')}</Text>
              )}
            </View>
          )}

          {(watch('requestlocation') === 'currentlocation' || watch('requestlocation') === 'selectedlocation') && (
            <View style={styles.locationDisplay}>
              <Text style={styles.locationText}>
                {customerRequestAdrress
                  ? String(customerRequestAdrress.formatted_address)
                  : 'Loading location...'}
              </Text>
            </View>
          )}

          {watch('requestlocation') === 'searchlocation' && customerRequestAdrress && (
            <View style={styles.locationDisplay}>
              <Text style={styles.locationText}>{String(customerRequestAdrress.formatted_address)}</Text>
              <TouchableOpacity
                style={styles.saveLocationButton}
                onPress={async () => {
                  try {
                    if (user?.uid && customerRequestAdrress) {
                      const locationData = {
                        name: String(customerRequestAdrress.formatted_address.split(',')[0] || 'Searched Location'),
                        address: String(customerRequestAdrress.formatted_address),
                        coordinates: customerRequestAdrress.location,
                      };

                      await VehicleService.saveLocation(user.uid, locationData);

                      // Refresh saved locations
                      await fetchSavedLocations();

                      Alert.alert('Success', 'Location saved!');
                    }
                  } catch (error) {
                    console.error('Error saving location:', error);
                    Alert.alert('Error', 'Failed to save location. Please try again.');
                  }
                }}
              >
                <Text style={styles.saveLocationText}>{AppStrings.MCX_SAVE_LOCATION_TEXT}</Text>
              </TouchableOpacity>
            </View>
          )}

          {watch('requestlocation') === 'savedlocation' && (
            <View style={styles.dropdownContainer}>
              <Text style={styles.dropdownLabel}>{AppStrings.MCX_SELECT_SAVED_LOCATION_LABEL}</Text>
              <CommonDropdown
                data={savedLocations.map((l) => ({ label: String(l.name), value: l.id }))}
                value={selectedSavedLocation}
                onValueChange={setSelectedSavedLocation}
                placeholder="Select saved location"
              />
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.servicesTitle}>{AppStrings.MCX_SERVICES_TITLE}</Text>
          {serviceFields.map((field, index) => (
            <View key={field.id} style={styles.serviceItem}>
              <View>
                <View style={styles.dropdownContainer}>
                  <Text style={styles.dropdownLabel}>{AppStrings.MCX_SERVICE_TYPE_KEYWORD}</Text>
                  <Controller
                    control={control}
                    name={`serviceNeed.${index}.vehicleservice`}
                    render={({ field: { onChange, value } }) => (
                      <CommonDropdown
                        data={
                          mechanic?.services
                            ? Object.keys(mechanic.services).map((key) => ({
                              label: serviceIdNameMap[key] || mechanic.services![key]['service-type'] || key,
                              value: key,
                            }))
                            : []
                        }
                        value={value}
                        onValueChange={onChange}
                        placeholder="Select service"
                      />
                    )}
                  />
                  {errors.serviceNeed && errors.serviceNeed[index]?.vehicleservice && (
                    <Text style={styles.errorText}>{errors.serviceNeed[index].vehicleservice.message}</Text>
                  )}
                  {index > 0 && (
                    <TouchableOpacity
                      style={styles.removeServiceButton}
                      onPress={() => removeService(index)}
                    >
                      <Text style={styles.removeServiceText}>{AppStrings.MCX_REMOVE_SERVICE_TEXT}</Text>
                    </TouchableOpacity>
                  )}
                </View>

                {watch(`serviceNeed.${index}.vehicleservice`) && (
                  <View style={styles.subServiceContainer}>
                    <Text style={styles.dropdownLabel}>{AppStrings.MCX_SUB_SERVICE_LABEL}</Text>
                    {(mechanic?.services && watch(`serviceNeed.${index}.vehicleservice`)) ? (
                      <Controller
                        control={control}
                        name={`serviceNeed.${index}.subServiceNeed`}
                        render={({ field: { onChange, value } }) => {
                          const selectedService = watch(`serviceNeed.${index}.vehicleservice`);
                          const subservices = mechanic?.services?.[selectedService]?.['sub-services'] || [];
                          const rate = value ? subServicePriceData[value]?.rate || subServicePriceData[value]?.price || null : null;
                          return (
                            <>
                              <CommonDropdown
                                data={subservices.map((sub: any) => ({
                                  label: subServiceIdNameMap[sub] || String(sub),
                                  value: String(sub),
                                }))}
                                value={value}
                                onValueChange={onChange}
                                placeholder="Select sub-service"
                              />
                              {rate !== null && (
                                <Text style={{ marginTop: 4, color: '#444', fontSize: 14 }}>
                                  Rate: ${rate}
                                </Text>
                              )}
                            </>
                          );
                        }}
                      />
                    ) : null}
                    {errors.serviceNeed && errors.serviceNeed[index]?.subServiceNeed && (
                      <Text style={styles.errorText}>{errors.serviceNeed[index].subServiceNeed.message}</Text>
                    )}
                  </View>
                )}
              </View>
            </View>
          ))}

          <TouchableOpacity
            style={styles.addServiceButton}
            onPress={() => appendService({ vehicleservice: '', subServiceNeed: '' })}
          >
            <Text style={styles.addServiceText}>{AppStrings.MCX_ADD_ANOTHER_SERVICE_TEXT}</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{AppStrings.MCX_SCREENING_DATA_TITLE}</Text>
          {Object.keys(screeningQuestions).reverse().map((key) => {
            const questionData = screeningQuestions[key];
            if (!questionData || questionData.hide) {
              return null;
            }
            if (key === 'que-6') {
              const rideShareAnswer = screeningAnswers['que-5']?.toLowerCase() || '';
              if (rideShareAnswer !== 'yes') {
                return null;
              }
            }
            if (questionData.conditional) {
              const conditionKey = questionData.conditional.dependsOn;
              const conditionValue = questionData.conditional.value;
              if (screeningAnswers[conditionKey] !== conditionValue) {
                return null;
              }
            }

            const hasError = screeningErrors.has(key);

            return (
              <View key={key} style={styles.dropdownContainer}>
                <Text style={styles.dropdownLabel}>{questionData.question}</Text>
                {questionData.type === 'entry' ? (
                  <TextInput
                    style={[styles.input, hasError ? styles.errorInput : undefined]}
                    placeholder={questionData.error}
                    value={screeningAnswers[key] || ''}
                    onChangeText={(text) => {
                      setScreeningAnswers({ ...screeningAnswers, [key]: text });
                      if (key === 'que-4') {
                        setScreeningErrors((prev) => {
                          const newErrors = new Set(prev);
                          const phoneNumber = text.trim();
                          if (phoneNumber && phoneNumber.length === 10 && /^\d{10}$/.test(phoneNumber)) {
                            newErrors.delete(key);
                          } else if (phoneNumber) {
                            newErrors.add(key);
                          } else {
                            newErrors.delete(key);
                          }
                          return newErrors;
                        });
                      } else {
                        setScreeningErrors((prev) => {
                          const newErrors = new Set(prev);
                          if (text.trim()) {
                            newErrors.delete(key);
                          } else {
                            newErrors.add(key);
                          }
                          return newErrors;
                        });
                      }
                    }}
                    keyboardType={key === 'que-4' ? 'numeric' : 'default'}
                    maxLength={key === 'que-4' ? 10 : undefined}
                  />
                ) : (
                  <CommonDropdown
                    data={questionData.options.map((option: string) => ({
                      label: option,
                      value: option.toLowerCase(),
                    }))}
                    value={screeningAnswers[key] || null}
                    onValueChange={(value) => {
                      setScreeningAnswers({ ...screeningAnswers, [key]: value });
                      setScreeningErrors((prev) => {
                        const newErrors = new Set(prev);
                        if (value !== null && value !== undefined && value.trim()) {
                          newErrors.delete(key);
                        } else {
                          newErrors.add(key);
                        }
                        return newErrors;
                      });
                    }}
                    placeholder="Choose option"
                    style={hasError ? styles.errorDropdown : undefined}
                  />
                )}
                {hasError && (
                  <Text style={styles.errorText}>
                    {key === 'que-4'
                      ? 'Please enter a valid 10-digit phone number'
                      : questionData.error
                    }
                  </Text>
                )}
              </View>
            );
          })}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{AppStrings.MCX_ADD_YOUR_OWN_PRICE_MODEL_TEXT}</Text>
          <View style={styles.priceModelTable}>
            <View style={styles.priceModelHeaderRow}>
              <Text style={[styles.priceModelHeaderCell, { flex: 3 }]}>{AppStrings.MCX_PRICE_MODEL_SERVICE_NAME}</Text>
              <Text style={[styles.priceModelHeaderCell, { flex: 2 }]}>{AppStrings.MCX_PRICE_MODEL_PRICE_RANGE}</Text>
              <Text style={[styles.priceModelHeaderCell, { flex: 2 }]}>{AppStrings.MCX_PRICE_MODEL_BID_RATE}</Text>
            </View>
            {serviceFields.map((field, index) => {
              const vehicleservice = watch(`serviceNeed.${index}.vehicleservice`);
              const subServiceNeed = watch(`serviceNeed.${index}.subServiceNeed`);
              const serviceName = serviceIdNameMap[vehicleservice] || vehicleservice || '';
              const subServiceName = subServiceIdNameMap[subServiceNeed] || subServiceNeed || '';
              const subServiceData = subServicePriceData[subServiceNeed] || {};
              const minPrice = subServiceData['min-price'] || subServiceData.minPrice || subServiceData.min_rate || null;
              const maxPrice = subServiceData['max-price'] || subServiceData.maxPrice || subServiceData.max_rate || null;
              let priceRange = '';
              if (minPrice !== null && maxPrice !== null) {
                priceRange = `$${minPrice} - $${maxPrice}`;
              } else if (minPrice !== null) {
                priceRange = `$${minPrice}`;
              } else {
                priceRange = subServiceData.rate || subServiceData.price || '';
                if (priceRange !== '') {
                  priceRange = `$${priceRange}`;
                }
              }
              const bidRate = bidRates[index] || '';
              return (
                <View key={`${field.id}-${subServiceNeed}`} style={styles.priceModelRow}>
                  <Text style={[styles.priceModelCell, { flex: 3 }]}>{serviceName} - {subServiceName}</Text>
                  <Text style={[styles.priceModelCell, { flex: 2 }]}>{priceRange}</Text>
                  <TextInput
                    style={[styles.priceModelCell, { flex: 2, borderWidth: 1, borderColor: '#ccc', borderRadius: 4, paddingHorizontal: 8 }]}
                    keyboardType="numeric"
                    value={bidRate}
                    onChangeText={(text) => {
                      const newBidRates = { ...bidRates, [index]: text };
                      setBidRates(newBidRates);
                    }}
                    placeholder="Enter bid rate"
                  />
                </View>
              );
            })}
            <View style={styles.priceModelTotalRow}>
              <Text style={[styles.priceModelTotalText, { flex: 3 }]}>{AppStrings.MCX_PRICE_MODEL_TOTAL}</Text>
              <Text style={[styles.priceModelTotalText, { flex: 2 }]} />
              <Text style={[styles.priceModelTotalText, { flex: 2 }]}>
                $ {Object.values(bidRates).reduce((acc, val) => acc + (parseFloat(val) || 0), 0).toFixed(2)}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{AppStrings.MCX_SELECT_TIME_TEXT}</Text>
          <View style={styles.dropdownContainer}>
            <Text style={styles.dropdownLabel}>{AppStrings.MCX_ALERTS_AND_CONFIRMATION_TEXT}</Text>
            <Controller
              control={control}
              name="alertconfirmation"
              render={({ field: { onChange, value } }) => (
                <CommonDropdown
                  data={alertConfirmationData}
                  value={value}
                  onValueChange={(val) => {
                    onChange(val);
                    _setAlertConfirmation(val);
                  }}
                  placeholder="Select alert and confirmation"
                />
              )}
            />
            {errors.alertconfirmation && <Text style={styles.errorText}>{errors.alertconfirmation.message}</Text>}
          </View>
          <View style={styles.dropdownContainer}>
            <Text style={styles.dropdownLabel}>{AppStrings.MCX_DATE_TEXT}</Text>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => setShowDatePicker(true)}
            >
              <Text style={[
                styles.dateInputText,
                !date && styles.dateInputPlaceholder,
              ]}>
                {date || 'MM/DD/YYYY'}
              </Text>
            </TouchableOpacity>
            {errors.scheduledate && <Text style={styles.errorText}>{errors.scheduledate.message}</Text>}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{AppStrings.MCX_NOTES_AND_IMAGES}</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder={AppStrings.MCX_NOTES_PLACEHOLDER_TEXT}
            value={notes}
            onChangeText={setNotes}
            multiline
          />
          <View>
            <TouchableOpacity style={styles.imageButton} onPress={_handleImagePick}>
              <Image source={AppCommonIcons.MCX_IMAGE_UPLOAD_ICON} style={styles.imageIcon} />
            </TouchableOpacity>

            <View style={styles.imageGrid}>
              {selectedImages.map((img, idx) => (
                <View key={`image-${idx}`} style={styles.imageContainer}>
                  <Image
                    source={{ uri: img.uri }}
                    style={styles.thumbnailImage}
                    resizeMode="cover"
                  />
                  <TouchableOpacity
                    style={styles.deleteButton}
                    onPress={() => handleDeleteImage(idx)}
                  >
                    <Image
                      source={require('../../../assets/common_icons/delete.png')}
                      style={styles.deleteIcon}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </View>
        </View>

        <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
          <Text style={styles.cancelButtonText}>{AppStrings.MCX_CANCEL_BUTTON_TEXT}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.confirmButton} onPress={handleSubmitWithValidation}>
          <Text style={styles.confirmButtonText}>{AppStrings.MCX_CONFIRM_APPOINTMENT_TEXT}</Text>
        </TouchableOpacity>
      </ScrollView>

      {Platform.OS === 'ios' ? (
        showDatePicker && (
          <DateTimePicker
            value={datePickerValue}
            mode="date"
            display="spinner"
            onChange={handleDateChange}
            style={{ width: '100%', backgroundColor: 'white' }}
          />
        )
      ) : (showDatePicker && (
        <DateTimePicker
          value={datePickerValue}
          mode="date"
          display="calendar"
          onChange={handleDateChange}
          minimumDate={new Date()}
          onTouchCancel={() => setShowDatePicker(false)}
        />)
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1f2a3a',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.COMMON_WHITE_SHADE,
    transform: [{ rotate: '180deg' }],
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 24,
  },
  scrollContent: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    color: Colors.COMMON_BlACK_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 12,
  },
  imageContainer: {
    position: 'relative',
    width: 80,
    height: 80,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 8,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
    gap: 8,
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  deleteButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  deleteIcon: {
    width: 12,
    height: 12,
    tintColor: '#FFFFFF',
  },

  mechanicCard: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  mechanicImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  mechanicInfo: {
    flex: 1,
  },
  mechanicName: {
    fontWeight: 'bold',
    fontSize: 16,
    color: Colors.TEXT_COLOR,
  },
  mechanicLocation: {
    fontSize: 14,
    color: Colors.PRIMARY_DARK,
  },
  ratingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  ratingStars: {
    color: Colors.PRIMARY,
    fontSize: 16,
  },
  availabilityText: {
    fontSize: 14,
    color: Colors.TEXT_COLOR,
  },
  appointmentText: {
    color: Colors.PRIMARY,
    fontWeight: 'bold',
  },
  dropdownContainer: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 12,
    marginBottom: 8,
  },
  dropdownLabel: {
    fontWeight: 'bold',
    color: Colors.TEXT_COLOR,
    marginBottom: 4,
  },
  input: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 12,
    fontSize: 16,
    color: Colors.TEXT_COLOR,
  },
  locationInputContainer: {
    marginBottom: 8,
    zIndex: 1000,
  },
  locationInput: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.TEXT_COLOR,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    zIndex: 1000,
  },
  locationDisplay: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 12,
    marginTop: 8,
  },
  locationText: {
    fontSize: 16,
    color: Colors.TEXT_COLOR,
  },
  optionText: {
    fontSize: 14,
    color: Colors.TEXT_COLOR,
  },
  radioGroup: {
    marginBottom: 12,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  radioCircle: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.TEXT_COLOR,
    marginRight: 8,
  },
  radioCircleSelected: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 6,
    borderColor: Colors.PRIMARY,
    marginRight: 8,
  },
  priceModelTable: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 8,
  },
  priceModelHeaderRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: Colors.PRIMARY,
    paddingBottom: 4,
  },
  priceModelHeaderCell: {
    fontWeight: 'bold',
    color: 'red',
    fontSize: 14,
  },
  priceModelRow: {
    flexDirection: 'row',
    paddingVertical: 8,
  },
  priceModelCell: {
    fontSize: 14,
    color: Colors.TEXT_COLOR,
  },
  priceModelTotalRow: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: Colors.PRIMARY,
    paddingTop: 8,
  },
  priceModelTotalText: {
    fontWeight: 'bold',
    color: 'red',
    fontSize: 14,
  },
  imageButton: {
    marginTop: 8,
    padding: 8,
    borderRadius: 4,
    backgroundColor: Colors.SECONDARY,
    alignItems: 'center',
    justifyContent: 'center',
    width: 48,
    height: 48,
  },
  imageIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.COMMON_WHITE_SHADE,
  },
  cancelButton: {
    backgroundColor: Colors.BACKGROUND,
    borderWidth: 2,
    borderColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    paddingVertical: 14,
    alignItems: 'center',
    marginBottom: 16,
  },
  cancelButtonText: {
    color: Colors.COMMON_BlACK_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
  },
  confirmButton: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 14,
    borderRadius: 4,
    alignItems: 'center',
    marginBottom: 32,
  },
  confirmButtonText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  errorText: {
    color: '#FF0000',
    fontWeight: '600',
    fontSize: 14,
    marginTop: 4,
    marginBottom: 4,
  },
  servicesTitle: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 18,
    marginBottom: 12,
    backgroundColor: '#9b0a0f',
    padding: 8,
  },
  serviceItem: {
    marginBottom: 16,
  },
  removeServiceButton: {
    backgroundColor: '#ff4444',
    padding: 8,
    borderRadius: 4,
    marginTop: 8,
    alignItems: 'center',
  },
  removeServiceText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
  },
  subServiceContainer: {
    marginTop: 8,
  },
  addServiceButton: {
    backgroundColor: Colors.PRIMARY,
    padding: 12,
    borderRadius: 4,
    alignItems: 'center',
    marginTop: 8,
  },
  addServiceText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
  },
  saveLocationButton: {
    marginTop: 8,
    padding: 8,
    borderRadius: 4,
    backgroundColor: Colors.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    width: 160,
  },
  saveLocationText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
  },
  errorInput: {
    borderColor: '#FF0000',
    borderWidth: 2,
  },
  errorDropdown: {
    borderColor: '#FF0000',
    borderWidth: 2,
    borderRadius: 4,
  },
  dateInput: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 12,
    minHeight: 40,
    justifyContent: 'center',
  },
  dateInputText: {
    fontSize: 16,
    color: Colors.TEXT_COLOR,
    fontWeight: '400',
  },
  dateInputPlaceholder: {
    color: '#999',
    fontWeight: '400',
  },
});

export default BookAppointmentsScreen;
