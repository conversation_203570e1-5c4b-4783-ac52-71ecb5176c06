import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Alert } from 'react-native';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import TitleSection from '../../../components/common/TitleSection';
import CustomButton from '../../../components/common/CustomButton';
import { Colors, Fonts, Sizes } from '../../../utils/constants/Theme';
import { AppCommonIcons, AppStrings } from '../../../utils/constants/AppStrings';
import { SafeAreaView } from 'react-native-safe-area-context';
const ReferFriend = () => {
  const handleInviteFriend = () => {
    // Handle invite friend action
    console.log('Invite friend pressed');
  };

  const handleCopyLink = () => {
    // Handle copy link action
    Alert.alert('Link Copied', 'Referral link has been copied to clipboard');
  };

  return (
    <SafeAreaView style={styles.mainContainer}  edges={['top', 'left', 'right']}>
      <TitleSection
        title={AppStrings.MCX_REFER_FRIEND_TITLE}
        bgColor={Colors.PRIMARY}
        textColor={Colors.COMMON_WHITE_SHADE}
        style={styles.titleSection}
      />
      <ScreenLayout
        useScrollView={false}
        useImageBackground={true}
        centerContent={false}
        useHorizontalPadding={true}
      >
        <View style={styles.whiteContainer}>
          {/* Main Content */}
          <View style={styles.mainContent}>
            {/* Refer a Friend Image */}
            <View style={styles.imageContainer}>
              <Image
                source={AppCommonIcons.MCX_REFER_FRIEND_ICON}
                style={styles.referImage}
                resizeMode="contain"
              />
            </View>

            {/* Earned Section */}
            <View style={styles.earnedSection}>
              <Text style={styles.earnedLabel}>{AppStrings.MCX_REFER_FRIEND_EARNED_TEXT}</Text>
              <Text style={styles.earnedAmount}>{AppStrings.MCX_DOLLOR_SYMBOL}</Text>
            </View>

            {/* Description */}
            <Text style={styles.subtitle}>{AppStrings.MCX_REFER_FRIEND_SUBTITLE}</Text>

            <Text style={styles.description}>
              {AppStrings.MCX_REFER_FRIEND_DESCRIPTION}
            </Text>

            {/* Referral Link */}
            <TouchableOpacity style={styles.linkContainer} onPress={handleCopyLink}>
              <Text style={styles.referralLink}>
                {AppStrings.MCX_REFER_FRIEND_COPY_LINK}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Fixed Bottom Button */}
          <View style={styles.buttonContainer}>
            <CustomButton
              text={AppStrings.MCX_REFER_FRIEND_INVITE_BUTTON}
              onPress={handleInviteFriend}
              variant="primary"
              size="medium"
              fullWidth={false}
              isBoldText={true}
              style={styles.inviteButton}
              textStyle={styles.inviteButtonText}
            />
          </View>
        </View>
      </ScreenLayout>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 0,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    justifyContent: 'space-between',
    marginTop: 20,
    marginBottom:20,
  },
  mainContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingTop: 30,
  },
  imageContainer: {
    alignItems: 'center',
    width: '100%',
  },
  referImage: {
    width: '90%',
    height: 250,
  },
  earnedSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  earnedLabel: {
    fontSize: Sizes.XXLARGE,
    fontWeight: '700',
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.SECONDARY,
  },
  earnedAmount: {
    fontSize: Sizes.XXXLARGE,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.PRIMARY,
    fontWeight: '700',
    paddingLeft:4,
  },
  subtitle: {
    fontSize: Sizes.LARGE,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.SECONDARY,
    textAlign: 'center',
    marginBottom: 12,
    fontWeight:'800',
    fontStyle: 'italic',
  },
  description: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '700',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  linkContainer: {
    backgroundColor: '#F0F0F0',
    paddingVertical: 14,
    paddingHorizontal: 16,
    marginBottom: 20,
    marginTop: 10,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  referralLink: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: '#007AFF',
    textAlign: 'center',
    textDecorationLine: 'underline',
    fontWeight: '500',
  },
  buttonContainer: {
    width: '100%',
    paddingBottom: 30,
    paddingTop: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  inviteButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 40,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    alignSelf: 'center',
    minWidth: 300,
    maxWidth: 450,
  },
  inviteButtonText: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: '700',
  },
  
});

export default ReferFriend;
