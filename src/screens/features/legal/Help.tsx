import React from 'react';
import { View, StyleSheet } from 'react-native';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import TitleSection from '../../../components/common/TitleSection';
import CommonCardStyle from '../../../components/common/CommonCardStyle';
 
import { Colors, Sizes } from '../../../utils/constants/Theme';
import { AppStrings } from '../../../utils/constants/AppStrings';
import { helpListData } from '../../../utils/templates/TemplateConfig';
import CustomListItem from '../../../components/common/CustomHelpListItem';

const Help = () => {
  const handleHelpItemPress = (helpItem: any) => {
    console.log('Help item pressed:', helpItem.question);
  };


  const renderHelpItems = () => {
    return helpListData.map((item, index) => (
      <View key={item.id} style={styles.helpItemContainer}>
        <CustomListItem
          title={item.question}
          onPress={() => handleHelpItemPress(item)}
          titleColor={Colors.COMMON_GREY_SHADE_LIGHT}
          titleSize={Sizes.MEDIUM}
          horizontalDivider={true}
          iconStyle={styles.cardIconStyle}
          backgroundColor= {Colors.COMMON_WHITE_SHADE}
        />
        {/* Add gap between items except for the last one */}
        {index < helpListData.length - 1 && <View style={styles.itemGap} />}
      </View>
    ));
  };

  return (
    <View style={styles.mainContainer}>
      <ScreenLayout
        useScrollView={true}
        centerContent={true}
      >
        {/* Title Section */}
        <TitleSection
          title={AppStrings.MCX_HELP_TITLE}
          bgColor={Colors.PRIMARY}
          textColor="#fff"
          style={styles.titleSection}
        />

        {/* FAQ Card with Help Items */}
        <CommonCardStyle
                        header={AppStrings.MCX_HELP_FAQ_SECTION}
                        headerColor={Colors.SECONDARY}
                        textColor={Colors.COMMON_WHITE_SHADE}
                        isCardContainerDecorated={true}
                        isTitleBordered={true}
                        cardBackgroundColor="transparent"
                    >
          <View style={styles.helpItemsContainer}>
            {renderHelpItems()}
          </View>
        </CommonCardStyle>
      </ScreenLayout>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 8,
  },
  helpItemContainer: {
    width: '100%',
    backgroundColor:'transparent'
  },
  helpItemsContainer: {
   flex:1,
   backgroundColor:'transparent'
  },
  itemGap: {
    height: 2,
  },
  cardIconStyle: {
    width: 24,
    height: 24,
    tintColor: Colors.PRIMARY,
  },
});

export default Help;
