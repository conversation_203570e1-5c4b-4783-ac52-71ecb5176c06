import React from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { WebView } from 'react-native-webview';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import TitleSection from '../../../components/common/TitleSection';
import { Colors, LayoutConstants } from '../../../utils/constants/Theme';

const TermsConditions = () => {
  const getHtmlSource = () => {
    if (Platform.OS === 'android') {
      return { uri: 'file:///android_asset/terms-conditions.html' };
    } else {
      return { uri: 'terms-conditions.html' };
    }
  };

  return (
    <View style={styles.mainContainer}>
      <TitleSection
        title="TERMS & CONDITIONS"
        bgColor={Colors.PRIMARY}
        textColor="#fff"
        style={styles.titleSection}
      />
      <ScreenLayout
        useScrollView={false}
        useImageBackground={true}
        centerContent={false}
        useHorizontalPadding={true}
      >
        <View style={styles.contentCard}>
          <WebView
            source={getHtmlSource()}
            style={styles.webView}
            showsVerticalScrollIndicator={false}
            scalesPageToFit={false}
            startInLoadingState={true}
            javaScriptEnabled={true}
            domStorageEnabled={true}
          />
        </View>
      </ScreenLayout>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 0,
  },
  contentCard: {
    flex: 1,
    backgroundColor: 'white',
    marginTop: 20,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  webView: {
    flex: 1,
    backgroundColor: 'white',
  },
});

export default TermsConditions;
