import React, { useState, useEffect, use<PERSON>allback, useMemo } from 'react';
import { View, Text, StyleSheet, FlatList, Alert, PermissionsAndroid, Platform } from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import CommonTextInput from '../../../components/common/CommonTextInput';
import CommonDropdown from '../../../components/common/CommonDropdown';
import LoaderOverlay from '../../../components/common/LoaderOverlay';
import MechanicCard from '../../../components/cardstyles/MechanicCard';
import { fmAvailabilityOptions } from '../../../utils/templates/TemplateConfig';
import { VehicleService } from '../../../utils/services/VehicleService';
import { Colors, Fonts, Sizes } from '../../../utils/constants/Theme';
import { AppStrings } from '../../../utils/constants/AppStrings';
import { calculateDistance, GC_NEAR_MILES } from '../../../utils/helpers/DistanceUtils';


interface MechanicItem {
  id: string;
  [key: string]: any;
}

const FindMechanic = () => {
  const [searchText, setSearchText] = useState<string>('');
  const [selectedServiceType, setSelectedServiceType] = useState<string | null>(null);
  const [selectedAvailability, setSelectedAvailability] = useState<string | null>(null);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [mechanics, setMechanics] = useState<MechanicItem[]>([]);
  const [serviceMap, setServiceMap] = useState<Record<string, string>>({});
  const [currentLocation, setCurrentLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  const serviceTypeDropdownData = useMemo(() => {
    return Object.keys(serviceMap).map(key => ({ label: serviceMap[key], value: key }));
  }, [serviceMap]);

  const availabilityDropdownData = fmAvailabilityOptions;


  const fetchMechanicsData = useCallback(async () => {
    try {
      setLoading(true);
      const mechanicsSnapshot = await VehicleService.getLoggedMechanics().once('value');
      const mechanicsData = mechanicsSnapshot.val() || {};
      const rawMechanics = Object.keys(mechanicsData).map(key => ({
        id: key,
        ...mechanicsData[key],
      }));
      setMechanics(rawMechanics);
      const _serviceMap = await VehicleService.getServiceIdNameMap();
      setServiceMap(_serviceMap);

    } catch (error) {
      console.error('Error fetching mechanics data:', error);
      Alert.alert('Error', 'Failed to load mechanics data');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchMechanicsData();
  }, [fetchMechanicsData]);

  useEffect(() => {
    const requestLocationPermission = async () => {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          getCurrentLocation();
        }
      } else {
        getCurrentLocation();
      }
    };

    const getCurrentLocation = () => {
      Geolocation.getCurrentPosition(
        (position) => {
          setCurrentLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });
        },
        (error) => {
          console.error('Error getting location:', error);
        },
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
      );
    };

    requestLocationPermission();
  }, []);

  const filteredMechanics = (mechanics || []).filter(mechanic => {
    const firstName = mechanic['first-name'] || '';
    const lastName = mechanic['last-name'] || '';
    const name = `${firstName} ${lastName}`.trim();
    const city = mechanic.city || '';
    const state = mechanic.state || '';
    const zipcode = mechanic.zipcode || '';
    const address1 = mechanic.address1 || '';
    const address2 = mechanic.address2 || '';
    const address = [address1, address2, city, state, zipcode].filter(Boolean).join(', ');
    const hasLocation = address1 || address2 || city || state || zipcode;
    const matchesSearch = name.toLowerCase().includes(searchText.toLowerCase()) ||
      address.toLowerCase().includes(searchText.toLowerCase());
    let matchesAvailability = true;
    if (selectedAvailability === 'Near Me') {
      if (currentLocation && mechanic.location && mechanic.location.latitude && mechanic.location.longitude) {
        const distance = calculateDistance(
          currentLocation.latitude,
          currentLocation.longitude,
          mechanic.location.latitude,
          mechanic.location.longitude
        );
        matchesAvailability = typeof distance === 'number' && distance < GC_NEAR_MILES;
      } else {
        matchesAvailability = false;
      }
    } else if (selectedAvailability === 'Open') {
      matchesAvailability = !!mechanic.availability;
    } else {
      matchesAvailability = !selectedAvailability || mechanic.availability === selectedAvailability;
    }
    const services = mechanic.services || {};
    const matchesServiceType = !selectedServiceType || services[selectedServiceType];
    return matchesSearch && matchesAvailability && matchesServiceType && hasLocation;
  });

  const toggleFavorite = (mechanicId: string) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(mechanicId)) {
      newFavorites.delete(mechanicId);
    } else {
      newFavorites.add(mechanicId);
    }
    setFavorites(newFavorites);
  };

  const renderMechanicCard = ({ item }: { item: MechanicItem }) => {
    const firstName = item['first-name'] || '';
    const lastName = item['last-name'] || '';
    const name = `${firstName} ${lastName}`.trim() || 'Unknown Mechanic';
    const city = item.city || '';
    const state = item.state || '';
    const zipcode = item.zipcode || '';
    const address1 = item.address1 || '';
    const address2 = item.address2 || '';
    const addressParts = [address1, address2, city, state, zipcode].filter(Boolean);
    const address = addressParts.length > 0 ? addressParts.join(', ') : 'Location not available';
    const userRating = item['mechanic-rating'] || 0;
    const ratingOutOf = item['rating-out-of'] || 5;
    let availability = 'Open';
    if (item.availability === true) {
      availability = 'Open';
    } else if (item.availability === false) {
      availability = 'Unavailable';
    } else if (typeof item.availability === 'string') {
      availability = item.availability;
    } else if (item.availability === undefined) {
      availability = 'Open';
    }
    const experience = item.experience || '';
    const gender = item.gender || '';
    const email = item.email || '';
    const mobile = item.mobile || '';
    const imageUrl = item.imageUrl || '';
    const dateOfBirth = item.dateofbirth || '';
    const maritalStatus = item.maritalstatus || '';
    const country = item.country || '';

    return (
      <MechanicCard
        id={parseInt(item.id, 10) || 0}
        name={name}
        address={address}
        userRating={userRating}
        ratingOutOf={ratingOutOf}
        availability={availability}
        isFavorite={favorites.has(item.id)}
        onFavoriteToggle={() => toggleFavorite(item.id)}
        showFavoriteIcon={favorites.has(item.id)}
        cardStyle={styles.findMechanicCard}
        experience={experience}
        gender={gender}
        email={email}
        mobile={mobile}
        imageUrl={imageUrl}
        dateOfBirth={dateOfBirth}
        maritalStatus={maritalStatus}
        country={country}
      />
    );
  };

  return (
    <ScreenLayout
      useScrollView={false}
      useImageBackground={true}
      centerContent={false}
       useHorizontalPadding={true}
    >
      <View style={styles.container}>
        <View style={styles.tableContainer}>
          <View style={styles.searchRow}>
            <CommonTextInput
              value={searchText}
              onChangeText={setSearchText}
              placeholder={AppStrings.MCX_SEARCH_NEARBY_TEXT}
              backgroundColor={Colors.SECONDARY}
              placeholderTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
            />
          </View>
          <View style={styles.filtersRow}>
            <View style={styles.filterCell}>
              <Text style={styles.filterLabel}>{AppStrings.MCX_SERVICE_TYPE_KEYWORD}</Text>
              <CommonDropdown
                data={serviceTypeDropdownData}
                value={selectedServiceType}
                onValueChange={setSelectedServiceType}
                placeholder={AppStrings.MCX_SERVICE_TYPE_TEXT}
                style={styles.dropdownStyle}
                placeholderTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
                selectedTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
                fontWeight="800"
              />
            </View>
            <View style={styles.filterCellLast}>
              <Text style={styles.filterLabel}>{AppStrings.MCX_MECHANIC_AVAILABILITY_TEXT}</Text>
              <CommonDropdown
                data={availabilityDropdownData}
                value={selectedAvailability}
                onValueChange={setSelectedAvailability}
                placeholder={AppStrings.MCX_SELECT_AVAILABILITY_TEXT}
                style={styles.dropdownStyle}
                placeholderTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
                selectedTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
                fontWeight="800"
              />
            </View>
          </View>
        </View>
        <LoaderOverlay visible={loading} />
        {!loading && (
          <FlatList
            data={filteredMechanics}
            keyExtractor={(item: MechanicItem) => item.id}
            renderItem={renderMechanicCard}
            style={styles.mechanicsList}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>{AppStrings.MCX_NO_MECHANICS_FOUND}</Text>
              </View>
            }
          />
        )}
      </View>
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tableContainer: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_DARK,
    marginTop: 16,
    marginHorizontal: 0,
  },
  searchRow: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.COMMON_GREY_SHADE_DARK,
    paddingVertical: 18,
  },
  searchInput: {
    marginLeft: 0,
    marginRight: 0,
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 0,
    marginTop: 0,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '800',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
  },
  filtersRow: {
    flexDirection: 'row',
    paddingHorizontal:16,
    paddingBottom:10,
  },
  filterCell: {
    flex: 1,
    paddingRight:8,
  },
  filterCellLast: {
    flex: 1,
    paddingLeft:8,
  },
  filterLabel: {
    fontSize: Sizes.SMALL,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    textTransform: 'uppercase',
  },
  dropdownStyle: {
    marginLeft: 0,
    marginRight: 0,
    marginBottom: 0,
    backgroundColor: Colors.SECONDARY,
    borderWidth: 0,
    borderRadius: 0,
  },
  mechanicsList: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 20,
  },
  findMechanicCard: {
    backgroundColor: '#fff',
    borderRadius: 2,
    padding: 16,
    marginBottom: 2,
  },

  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  emptyText: {
    fontSize: Sizes.MEDIUM,
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
  },

});

export default FindMechanic;