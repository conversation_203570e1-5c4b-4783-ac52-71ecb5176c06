import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StyleSheet,
  Alert,
} from 'react-native';
import LoaderOverlay from '../../components/common/LoaderOverlay';
import {Colors, Fonts} from '../../utils/constants/Theme';
import {AppStrings} from '../../utils/constants/AppStrings';
import {wp} from '../../utils/ResponsiveParams';
import {useNavigation} from '@react-navigation/native';
import {useAuth} from '../../utils/configs/AuthContext';

import {GC_CUSTOMER_ID, GC_SOCIAL_LOGGED_IN} from '../../utils/globals';
import AsyncStorage from '@react-native-async-storage/async-storage';

const AppLoginPageScreen = () => {
  const navigation = useNavigation();

   const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('Seby@123#');
  const [emailError, setEmailError] = useState('');
  const [loading, setLoading] = useState(false);

  const {signIn} = useAuth();

  const validateEmail = (value: string) => {
    // Simple email regex
    const re = /\S+@\S+\.\S+/;
    return re.test(value);
  };

  const handleEmailBlur = () => {
    if (email && !validateEmail(email)) {
      setEmailError('Please enter a valid email address');
    } else {
      setEmailError('');
    }
  };

  const handleSignupClick = async () => {
    await AsyncStorage.setItem(GC_CUSTOMER_ID, '');
  };

  const handleResetPasswordClick = async () => {
    await AsyncStorage.setItem(GC_CUSTOMER_ID, '');
  };
  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }
    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }

    setLoading(true);
    try {
      await AsyncStorage.setItem(GC_SOCIAL_LOGGED_IN, 'false');
      await signIn(email, password);
      //await AsyncStorage.setItem(GC_CUSTOMER_ID, resLogin.user.uid);
      navigation.reset({
        index: 0,
        routes: [{name: 'DashBoard' as never}],
      });
    } catch (error: any) {
      console.error('Login error:', error);
      Alert.alert(
        'Login Failed',
        error.message || 'An error occurred during login',
      );
    } finally {
      setLoading(false);
    }
  };
  return (
    <View style={styles.container}>
      <LoaderOverlay visible={loading} />
      <Image
        source={require('../../assets/mycanx_logo.png')}
        style={styles.logo}
        resizeMode="contain"
      />

      {emailError ? (
        <Text style={{color: 'red', alignSelf: 'flex-start', marginBottom: 4}}>
          {emailError}
        </Text>
      ) : null}
      <TextInput
        style={[styles.input, emailError && {marginBottom: 14}]}
        placeholder={AppStrings.MCX_ENTER_EMAIL}
        placeholderTextColor="#fff"
        value={email}
        onChangeText={setEmail}
        onBlur={handleEmailBlur}
        keyboardType="email-address"
        autoCapitalize="none"
      />
      <TextInput
        style={styles.input}
        placeholder={AppStrings.MCX_ENTER_PASSWORD}
        placeholderTextColor="#fff"
        secureTextEntry
        value={password}
        onChangeText={setPassword}
      />

      <TouchableOpacity style={styles.loginBtn} onPress={handleLogin}>
        <Text style={styles.loginText}>
          {AppStrings.MCX_LOGIN.toUpperCase()}
        </Text>
      </TouchableOpacity>

      <View style={styles.bottomRow}>
        <TouchableOpacity onPress={handleResetPasswordClick}>
          <Text style={styles.forgotText}>
            {AppStrings.MCX_FORGOT_PASSWORD}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.signUpBtn} onPress={handleSignupClick}>
          <Text style={styles.signUpText}>
            {AppStrings.MCX_SIGN_UP.toUpperCase()}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default AppLoginPageScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.BACKGROUND,
    padding: 14,
  },
  logo: {
    height: 180,
    width: 180,
    marginBottom: 40,
  },
  input: {
    width: '100%',
    backgroundColor: Colors.LOGIN_INPUT_TEXT_BG_COLOR,
    borderRadius: 2,
    paddingVertical: 10,
    paddingHorizontal: 16,
    color: '#fff',
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 14,
    fontSize: 14,
    alignItems: 'center',
  },
  loginBtn: {
    width: '100%',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 2,
    paddingVertical: 10,
    alignItems: 'center',
    marginTop: 12,
    marginBottom: 18,
  },
  loginText: {
    color: '#fff',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
  },
  bottomRow: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 8,
  },
  forgotText: {
    color: Colors.BOTTOM_SIGNUP_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 13,
  },
  signUpBtn: {
    borderColor: Colors.BOTTOM_SIGNUP_TEXT_COLOR,
    borderWidth: 1,
    borderRadius: 2,
    paddingVertical: 8,
    paddingHorizontal: wp(15),
    marginLeft: wp(20),
  },
  signUpText: {
    color: Colors.BOTTOM_SIGNUP_TEXT_COLOR,
    fontFamily: Fonts.ROBO_BOLD,
    fontSize: 14,
    fontWeight: 'bold',
  },
});
/* import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StyleSheet,
  Alert,
  SafeAreaView,
  StatusBar,
  Dimensions,
  Animated,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import {Colors, Fonts} from '../../utils/constants/Theme';
import {AppStrings, RouteNames} from '../../utils/constants/AppStrings';
import {wp} from '../../utils/ResponsiveParams';
import {useNavigation} from '@react-navigation/native';
import {useAuth} from '../../utils/configs/AuthContext';

const {width, height} = Dimensions.get('window');

const AppLoginPageScreen = () => {
  const navigation = useNavigation();
  const {signIn} = useAuth();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Animation values
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  React.useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const validateEmail = (value: string) => {
    const re = /\S+@\S+\.\S+/;
    return re.test(value);
  };

  const handleEmailBlur = () => {
    if (email && !validateEmail(email)) {
      setEmailError('Please enter a valid email address');
    } else {
      setEmailError('');
    }
  };

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }
    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    try {
      await signIn(email, password);
      navigation.reset({
        index: 0,
        routes: [{name: 'DashBoard' as never}],
      });
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />

      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.gradientBackground}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 1}}>
        <View style={[styles.floatingCircle, styles.circle1]} />
        <View style={[styles.floatingCircle, styles.circle2]} />

        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}>
          <Animated.View
            style={[
              styles.container,
              {
                opacity: fadeAnim,
                transform: [{translateY: slideAnim}],
              },
            ]}>
            <View style={styles.headerSection}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => navigation.goBack()}>
                <Icon name="arrow-left" size={24} color="white" />
              </TouchableOpacity>

              <View style={styles.logoContainer}>
                <Image
                  source={require('../../assets/mycanx_logo.png')}
                  style={styles.logo}
                  resizeMode="contain"
                />
                <View style={styles.logoGlow} />
              </View>

              <Text style={styles.welcomeText}>Sign In</Text>
              <Text style={styles.subtitleText}>
                Enter your credentials to continue
              </Text>
            </View>

            <View style={styles.formSection}>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Email Address</Text>
                <View
                  style={[
                    styles.inputWrapper,
                    emailError && styles.inputError,
                  ]}>
                  <Icon
                    name="email-outline"
                    size={20}
                    color="#a0a0a0"
                    style={styles.inputIcon}
                  />
                  <TextInput
                    style={styles.input}
                    placeholder={AppStrings.MCX_ENTER_EMAIL}
                    placeholderTextColor="#666"
                    value={email}
                    onChangeText={setEmail}
                    onBlur={handleEmailBlur}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                </View>
                {emailError ? (
                  <Text style={styles.errorText}>{emailError}</Text>
                ) : null}
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Password</Text>
                <View style={styles.inputWrapper}>
                  <Icon
                    name="lock-outline"
                    size={20}
                    color="#a0a0a0"
                    style={styles.inputIcon}
                  />
                  <TextInput
                    style={styles.input}
                    placeholder={AppStrings.MCX_ENTER_PASSWORD}
                    placeholderTextColor="#666"
                    secureTextEntry={!showPassword}
                    value={password}
                    onChangeText={setPassword}
                    autoCorrect={false}
                  />
                  <TouchableOpacity
                    style={styles.eyeIcon}
                    onPress={() => setShowPassword(!showPassword)}>
                    <Icon
                      name={showPassword ? 'eye-off-outline' : 'eye-outline'}
                      size={20}
                      color="#a0a0a0"
                    />
                  </TouchableOpacity>
                </View>
              </View>

              <TouchableOpacity
                style={styles.loginBtn}
                onPress={handleLogin}
                disabled={isLoading}
                activeOpacity={0.8}>
                <LinearGradient
                  colors={isLoading ? ['#999', '#666'] : ['#ff6b6b', '#ee5a24']}
                  style={styles.buttonGradient}
                  start={{x: 0, y: 0}}
                  end={{x: 1, y: 0}}>
                  {isLoading ? (
                    <View style={styles.loadingContainer}>
                      <Text style={styles.loginText}>Signing In...</Text>
                    </View>
                  ) : (
                    <>
                      <Icon
                        name="login"
                        size={20}
                        color="white"
                        style={styles.buttonIcon}
                      />
                      <Text style={styles.loginText}>
                        {AppStrings.MCX_LOGIN.toUpperCase()}
                      </Text>
                    </>
                  )}
                </LinearGradient>
              </TouchableOpacity>

              <TouchableOpacity style={styles.forgotPasswordContainer}>
                <Text style={styles.forgotText}>
                  {AppStrings.MCX_FORGOT_PASSWORD}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.bottomSection}>
              <View style={styles.dividerContainer}>
                <View style={styles.dividerLine} />
                <Text style={styles.dividerText}>or</Text>
                <View style={styles.dividerLine} />
              </View>

              <View style={styles.bottomRow}>
                <Text style={styles.noAccountText}>Don't have an account?</Text>
                <TouchableOpacity
                  style={styles.signUpBtn}
                  onPress={() =>
                    navigation.navigate(
                      RouteNames.MCX_NAV_ACCOUNT_REGISTRATION as never,
                    )
                  }
                  activeOpacity={0.8}>
                  <Text style={styles.signUpText}>
                    {AppStrings.MCX_SIGN_UP}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </Animated.View>
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  );
};

export default AppLoginPageScreen;

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  gradientBackground: {
    flex: 1,
    position: 'relative',
  },
  floatingCircle: {
    position: 'absolute',
    borderRadius: 100,
    opacity: 0.08,
  },
  circle1: {
    width: 180,
    height: 180,
    backgroundColor: '#ff6b6b',
    top: -40,
    right: -40,
  },
  circle2: {
    width: 120,
    height: 120,
    backgroundColor: '#74b9ff',
    bottom: 80,
    left: -30,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 24,
  },
  headerSection: {
    alignItems: 'center',
    paddingTop: 20,
    marginBottom: 40,
  },
  backButton: {
    position: 'absolute',
    left: 0,
    top: 20,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  logoContainer: {
    position: 'relative',
    marginBottom: 24,
    marginTop: 40,
  },
  logo: {
    height: 100,
    width: 100,
    zIndex: 2,
  },
  logoGlow: {
    position: 'absolute',
    top: 8,
    left: 8,
    right: 8,
    bottom: 8,
    borderRadius: 50,
    backgroundColor: '#ff6b6b',
    opacity: 0.2,
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.6,
    shadowRadius: 15,
    elevation: 8,
  },
  welcomeText: {
    fontSize: 32,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
  },
  subtitleText: {
    fontSize: 16,
    color: '#a0a0a0',
    textAlign: 'center',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  formSection: {
    flex: 1,
    justifyContent: 'center',
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    color: 'white',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: '600',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    height: 56,
  },
  inputError: {
    borderColor: '#ff6b6b',
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    color: 'white',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
    paddingVertical: 0,
  },
  inputWithError: {
    marginBottom: 14,
  },
  eyeIcon: {
    padding: 4,
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 14,
    marginTop: 8,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  loginBtn: {
    borderRadius: 16,
    marginTop: 8,
    marginBottom: 24,
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 16,
  },
  buttonIcon: {
    marginRight: 12,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loginText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  forgotPasswordContainer: {
    alignItems: 'center',
  },
  forgotText: {
    color: '#74b9ff',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
    fontWeight: '500',
  },
  bottomSection: {
    paddingBottom: 30,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  dividerText: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: 14,
    fontFamily: Fonts.ROBO_REGULAR,
    paddingHorizontal: 16,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noAccountText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
  },
  signUpBtn: {
    marginLeft: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  signUpText: {
    color: '#ff6b6b',
    fontFamily: Fonts.ROBO_BOLD,
    fontSize: 16,
    fontWeight: '600',
  },
});
 */
