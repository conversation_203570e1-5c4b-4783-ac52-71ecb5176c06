import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ImageStyle,
  ViewStyle,
  TextStyle,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {AppStrings, RouteNames} from '../../utils/constants/AppStrings';
import CommonLink from '../../components/common/CommonLinkComponent';
import {useNavigation} from '@react-navigation/native';
import {Colors, Fonts} from '../../utils/constants/Theme';
import HorizontalDivider from '../../components/common/HorizontalDivider';
import GoogleAuthService from '../../auth/google/googleAuthService';

import {
  GC_CUSTOMER_ID,
  GC_SIGNUP_PROVIDER_TYPE_ID_PREF,
  GC_SIGNUP_PROVIDER_TYPE_PREF,
} from '../../utils/globals';
import AsyncStorage from '@react-native-async-storage/async-storage';

const LoginMainScreen = () => {
  const navigation = useNavigation();
  const {signIn, error} = GoogleAuthService();

  const onSignupButtonPress = async () => {
    await AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_PREF, 'normal');
    await AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_ID_PREF, '');
    await AsyncStorage.setItem(GC_CUSTOMER_ID, '');

    navigation.navigate(RouteNames.MCX_NAV_ACCOUNT_REGISTRATION as never);
  };

  const onSignInButtonPress = async () => {
    await AsyncStorage.setItem(GC_CUSTOMER_ID, '');
    navigation.navigate(RouteNames.MCX_NAV_AppLoginPageScreen as never);
  };
  const onGoogleButtonPress = async () => {
    try {
      const loggedUser = await signIn();
      Alert.alert('Login Success', `Welcome ${loggedUser.displayName}`);
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const onFacebookButtonPress = async () => {
    try {
      Alert.alert('Info', 'Facebook login will be implemented');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  return (
    <View style={styles.container}>
      <Image
        source={require('../../assets/mycanx_logo.png')}
        style={styles.logo}
        resizeMode="contain"
      />

      <TouchableOpacity style={styles.signUpBtn} onPress={onSignupButtonPress}>
        <Text style={styles.signUpText}>{AppStrings.MCX_SIGN_UP}</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.loginBtn} onPress={onSignInButtonPress}>
        <Text style={styles.loginText}>{AppStrings.MCX_LOGIN}</Text>
      </TouchableOpacity>

      <Text style={styles.orText}>{AppStrings.MCX_OR_WITH}</Text>

      <View style={styles.socialRow}>
        <TouchableOpacity
          style={[styles.socialButton, styles.facebookButton]}
          onPress={onFacebookButtonPress}>
          <Icon name="facebook" size={24} color={Colors.BUTTON_TEXT_COLOR} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.socialButton, styles.googleButton]}
          onPress={onGoogleButtonPress}>
          <Image
            source={require('../../assets/social-network-icons/google-icon.png')}
            style={styles.socialIcon}
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>
      {/* Divider line */}
      <HorizontalDivider isFullWidth={true} />
      <Text style={styles.termsText}>
        {AppStrings.MCX_TERMS_PREFIX}
        {'\n'}
        <CommonLink
          url="https://your-privacy-policy-url.com"
          style={styles.linkText}>
          {AppStrings.MCX_PRIVACY_POLICY}
        </CommonLink>{' '}
        {AppStrings.MCX_AND_KEYWORD}{' '}
        <CommonLink
          url="https://your-terms-of-service-url.com"
          style={styles.linkText}>
          {AppStrings.MCX_TERMS_OF_SERVICE}
        </CommonLink>
      </Text>
    </View>
  );
};

export default LoginMainScreen;

type Styles = {
  container: ViewStyle;
  logo: ImageStyle;
  signUpBtn: ViewStyle;
  loginBtn: ViewStyle;
  socialRow: ViewStyle;
  socialButton: ViewStyle;
  facebookButton: ViewStyle;
  googleButton: ViewStyle;
  socialIcon: ImageStyle;
  signUpText: TextStyle;
  loginText: TextStyle;
  orText: TextStyle;
  termsText: TextStyle;
  linkText: TextStyle;
};

const styles = StyleSheet.create<Styles>({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.BACKGROUND,
  },
  logo: {
    height: 180,
    width: 180,
    marginBottom: 40,
  },
  signUpBtn: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 8,
    paddingHorizontal: 80,
    borderRadius: 2,
    marginBottom: 10,
    alignItems: 'center',
    alignSelf: 'stretch',
    marginHorizontal: 14,
  },
  loginBtn: {
    backgroundColor: 'transparent',
    borderColor: Colors.BORDER_COLOR,
    borderWidth: 1.2,
    paddingVertical: 8,
    paddingHorizontal: 80,
    borderRadius: 2,
    marginBottom: 30,
    alignItems: 'center',
    alignSelf: 'stretch',
    marginHorizontal: 14,
  },
  socialRow: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  socialButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.BORDER_COLOR,
    marginHorizontal: 12,
    padding: 10,
  },
  facebookButton: {
    backgroundColor: '#3b5998',
    borderColor: '#3b5998',
  },
  googleButton: {
    backgroundColor: Colors.BACKGROUND,
  },
  socialIcon: {
    width: 24,
    height: 24,
  },
  signUpText: {
    color: Colors.BUTTON_TEXT_COLOR,
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  loginText: {
    color: '#a10000',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  orText: {
    color: '#999',
    fontSize: 12,
    marginBottom: 10,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  termsText: {
    fontSize: 12,
    color: Colors.TERMS_TEXT_COLOR,
    textAlign: 'center',
    paddingHorizontal: 30,
    fontFamily: Fonts.ROBO_REGULAR,
    lineHeight: 25,
  },
  linkText: {
    color: Colors.LINK_TEXT_COLOR,
    fontWeight: 'bold',
    fontFamily: Fonts.ROBO_BOLD,
  },
});
/* import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ImageStyle,
  ViewStyle,
  TextStyle,
  Dimensions,
  StatusBar,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import {AppStrings, RouteNames} from '../../utils/constants/AppStrings';
import CommonLink from '../../components/common/CommonLinkComponent';
import {useNavigation} from '@react-navigation/native';
import {Fonts} from '../../utils/constants/Theme';
import GoogleAuthService from '../../auth/google/googleAuthService';

const {height} = Dimensions.get('window');

const LoginMainScreen = () => {
  const navigation = useNavigation();
  const {signIn, error} = GoogleAuthService();

  const onGoogleButtonPress = async () => {
    try {
      const loggedUser = await signIn();
      Alert.alert('Login Success', `Welcome ${loggedUser.displayName}`);
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const onFacebookButtonPress = async () => {
    try {
      Alert.alert('Info', 'Facebook login will be implemented');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />

      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.gradientBackground}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 1}}>
        <View style={[styles.floatingCircle, styles.circle1]} />
        <View style={[styles.floatingCircle, styles.circle2]} />
        <View style={[styles.floatingCircle, styles.circle3]} />

        <View style={styles.container}>
          <View style={styles.headerSection}>
            <View style={styles.logoContainer}>
              <Image
                source={require('../../assets/mycanx_logo.png')}
                style={styles.logo}
                resizeMode="contain"
              />
              <View style={styles.logoGlow} />
            </View>

            <Text style={styles.welcomeText}>Welcome Back</Text>
            <Text style={styles.subtitleText}>
              Connect with trusted mechanics near you
            </Text>
          </View>

          <View style={styles.actionsSection}>
            <TouchableOpacity
              style={styles.signUpBtn}
              onPress={() =>
                navigation.navigate(
                  RouteNames.MCX_NAV_ACCOUNT_REGISTRATION as never,
                )
              }
              activeOpacity={0.8}>
              <LinearGradient
                colors={['#ff6b6b', '#ee5a24']}
                style={styles.buttonGradient}
                start={{x: 0, y: 0}}
                end={{x: 1, y: 0}}>
                <Icon
                  name="account-plus"
                  size={20}
                  color="white"
                  style={styles.buttonIcon}
                />
                <Text style={styles.signUpText}>{AppStrings.MCX_SIGN_UP}</Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.loginBtn}
              onPress={() =>
                navigation.navigate(
                  RouteNames.MCX_NAV_AppLoginPageScreen as never,
                )
              }
              activeOpacity={0.8}>
              <View style={styles.loginButtonContent}>
                <Icon
                  name="login"
                  size={20}
                  color="#ff6b6b"
                  style={styles.buttonIcon}
                />
                <Text style={styles.loginText}>{AppStrings.MCX_LOGIN}</Text>
              </View>
            </TouchableOpacity>

            <View style={styles.socialSection}>
              <View style={styles.dividerContainer}>
                <View style={styles.dividerLine} />
                <Text style={styles.orText}>{AppStrings.MCX_OR_WITH}</Text>
                <View style={styles.dividerLine} />
              </View>

              <View style={styles.socialRow}>
                <TouchableOpacity
                  style={[styles.socialButton, styles.facebookButton]}
                  onPress={onFacebookButtonPress}
                  activeOpacity={0.8}>
                  <LinearGradient
                    colors={['#4267B2', '#365899']}
                    style={styles.socialButtonGradient}>
                    <Icon name="facebook" size={28} color="white" />
                  </LinearGradient>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.socialButton, styles.googleButton]}
                  onPress={onGoogleButtonPress}
                  activeOpacity={0.8}>
                  <View style={styles.googleButtonContent}>
                    <Image
                      source={require('../../assets/social-network-icons/google-icon.png')}
                      style={styles.socialIcon}
                      resizeMode="contain"
                    />
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          <View style={styles.footerSection}>
            <Text style={styles.termsText}>
              {AppStrings.MCX_TERMS_PREFIX}
              {'\n'}
              <CommonLink
                url="https://your-privacy-policy-url.com"
                style={styles.linkText}>
                {AppStrings.MCX_PRIVACY_POLICY}
              </CommonLink>{' '}
              {AppStrings.MCX_AND_KEYWORD}{' '}
              <CommonLink
                url="https://your-terms-of-service-url.com"
                style={styles.linkText}>
                {AppStrings.MCX_TERMS_OF_SERVICE}
              </CommonLink>
            </Text>
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

export default LoginMainScreen;

type Styles = {
  safeArea: ViewStyle;
  gradientBackground: ViewStyle;
  floatingCircle: ViewStyle;
  circle1: ViewStyle;
  circle2: ViewStyle;
  circle3: ViewStyle;
  container: ViewStyle;
  headerSection: ViewStyle;
  logoContainer: ViewStyle;
  logo: ImageStyle;
  logoGlow: ViewStyle;
  welcomeText: TextStyle;
  subtitleText: TextStyle;
  actionsSection: ViewStyle;
  signUpBtn: ViewStyle;
  loginBtn: ViewStyle;
  buttonGradient: ViewStyle;
  loginButtonContent: ViewStyle;
  buttonIcon: TextStyle;
  socialSection: ViewStyle;
  dividerContainer: ViewStyle;
  dividerLine: ViewStyle;
  socialRow: ViewStyle;
  socialButton: ViewStyle;
  facebookButton: ViewStyle;
  googleButton: ViewStyle;
  socialButtonGradient: ViewStyle;
  googleButtonContent: ViewStyle;
  socialIcon: ImageStyle;
  footerSection: ViewStyle;
  signUpText: TextStyle;
  loginText: TextStyle;
  orText: TextStyle;
  termsText: TextStyle;
  linkText: TextStyle;
};

const styles = StyleSheet.create<Styles>({
  safeArea: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  gradientBackground: {
    flex: 1,
    position: 'relative',
  },
  floatingCircle: {
    position: 'absolute',
    borderRadius: 100,
    opacity: 0.1,
  },
  circle1: {
    width: 200,
    height: 200,
    backgroundColor: '#ff6b6b',
    top: -50,
    right: -50,
  },
  circle2: {
    width: 150,
    height: 150,
    backgroundColor: '#74b9ff',
    bottom: 100,
    left: -30,
  },
  circle3: {
    width: 100,
    height: 100,
    backgroundColor: '#00b894',
    top: height * 0.3,
    right: 20,
  },
  container: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  headerSection: {
    alignItems: 'center',
    paddingTop: height * 0.08,
  },
  logoContainer: {
    position: 'relative',
    marginBottom: 30,
  },
  logo: {
    height: 120,
    width: 120,
    zIndex: 2,
  },
  logoGlow: {
    position: 'absolute',
    top: 10,
    left: 10,
    right: 10,
    bottom: 10,
    borderRadius: 60,
    backgroundColor: '#ff6b6b',
    opacity: 0.3,
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.8,
    shadowRadius: 20,
    elevation: 10,
  },
  welcomeText: {
    fontSize: 28,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: Fonts.ROBO_BOLD,
  },
  subtitleText: {
    fontSize: 16,
    color: '#a0a0a0',
    textAlign: 'center',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  actionsSection: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 20,
  },
  signUpBtn: {
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 16,
  },
  loginBtn: {
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#ff6b6b',
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
    marginBottom: 32,
  },
  loginButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
  },
  buttonIcon: {
    marginRight: 12,
  },
  socialSection: {
    alignItems: 'center',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    width: '100%',
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  socialRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 20,
  },
  socialButton: {
    width: 60,
    height: 60,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  facebookButton: {},
  googleButton: {
    backgroundColor: 'white',
  },
  socialButtonGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  googleButtonContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  socialIcon: {
    width: 32,
    height: 32,
  },
  footerSection: {
    paddingBottom: 20,
  },
  signUpText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  loginText: {
    color: '#ff6b6b',
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  orText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontFamily: Fonts.ROBO_REGULAR,
    paddingHorizontal: 16,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  termsText: {
    fontSize: 13,
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
    lineHeight: 20,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  linkText: {
    color: '#74b9ff',
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
});
 */
