import React, { useEffect } from 'react';
import { View, Image, StyleSheet, Platform, PermissionsAndroid } from 'react-native';
import { hp, wp } from '../../utils/ResponsiveParams';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppCommonIcons } from '../../utils/constants/AppStrings';
import Geolocation from 'react-native-geolocation-service';
import { useAuth } from '../../utils/configs/AuthContext';
import { VehicleService } from '../../utils/services/VehicleService';

type RootStackParamList = {
  LoginMainScreen: undefined;
};

type SplashScreenProps = {
  navigation: StackNavigationProp<RootStackParamList, 'LoginMainScreen'>;
};

const SplashScreen: React.FC<SplashScreenProps> = ({ navigation }) => {
  const { user } = useAuth();

  useEffect(() => {
    checkAndRequestPermission();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const navigateToNextScreen = () => {
    navigation.replace('LoginMainScreen');
  };

  const checkAndRequestPermission = async () => {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
        );
        if (granted) {
          getCurrentLocation();
          navigateToNextScreen();
        } else {
          handleAllowPermission();
        }
      } else {
        getCurrentLocation();
        navigateToNextScreen();
      }
    } catch (error) {
      console.log('Error checking/requesting location permission:', error);
    }
  };

  const handleAllowPermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        console.log('Location permission granted permanently');
        getCurrentLocation();
        navigateToNextScreen();
      } else {
        console.log('Location permission denied');
        // Stay on splash screen if permission denied
      }
    } catch (error) {
      console.log('Error requesting location permission:', error);
    }
  };

  const getCurrentLocation = () => {
    Geolocation.getCurrentPosition(
    async (position) => {
      console.log('Current position:', position);
      if (user?.uid) {
        try {
          await VehicleService.updateCustomerLocation(user.uid, {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });
          console.log('Customer location updated successfully');
        } catch (error) {
          console.error('Error updating customer location:', error);
        }
      }
    },
    (error) => {
      console.log('Error getting location:', error);
    },
    { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
  );
};

  return (
    <View style={styles.container}>
      <Image
        source={AppCommonIcons.MCX_APP_ICON}
        style={styles.logo}
        resizeMode="contain"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    width: wp(60),
    height: hp(30),
  },
});

export default SplashScreen;
