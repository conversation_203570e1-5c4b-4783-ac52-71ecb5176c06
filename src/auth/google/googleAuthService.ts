import {useEffect, useState} from 'react';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import { FirebaseAuthTypes } from '@react-native-firebase/auth';
import { getAuth, onAuthStateChanged, signInWithCredential } from '@react-native-firebase/auth';
import { getApp } from '@react-native-firebase/app';

GoogleSignin.configure({
  webClientId:
    '416381233217-hmcpjknudbm78mbgcdid91ph29tukm0u.apps.googleusercontent.com',
  scopes: ['profile', 'email'],
  offlineAccess: true, // For server-side access, if needed
});

const GoogleAuthService = () => {
  const [user, setUser] = useState<FirebaseAuthTypes.User | null>(null); // Use Firebase auth.User type
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Listen for auth state changes
    const app = getApp();
    const auth = getAuth(app);
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser: FirebaseAuthTypes.User | null) => {
      console.log('Firebase auth state changed:', {
        uid: firebaseUser?.uid,
        email: firebaseUser?.email,
        provider: firebaseUser?.providerData?.[0]?.providerId,
      });
      setUser(firebaseUser);
    });

    // Cleanup listener on unmount
    return () => unsubscribe();
  }, []);

  const signIn = async () => {
    try {
      setLoading(true);
      setError(null);

      // Ensure previous Google session is cleared
      await GoogleSignin.signOut();

      // Perform Google Sign-In
      await GoogleSignin.hasPlayServices(); // Check Google Play Services availability
      await GoogleSignin.signIn();

      // Get tokens (idToken for Firebase)
      const {idToken} = await GoogleSignin.getTokens();
      if (!idToken) {
        throw new Error('Google Sign-In failed: No idToken returned');
      }

      // Create a Google credential with the idToken
      const auth = getAuth(getApp());
      const {GoogleAuthProvider} = require('@react-native-firebase/auth');
      const googleCredential = GoogleAuthProvider.credential(idToken);

      // Sign in to Firebase
      const userCredential = await signInWithCredential(auth, googleCredential);

      // Log success (user state is updated via onAuthStateChanged)
      return userCredential.user; // Optional return for caller
    } catch (err: any) {
      console.error('Google Sign-In Error:', JSON.stringify(err, null, 2));
      let errorMessage = 'Google Sign-In failed';

      // Handle specific Google Sign-In errors
      if (err.code) {
        switch (err.code) {
          case statusCodes.SIGN_IN_CANCELLED:
            errorMessage = 'Sign-in was cancelled';
            break;
          case statusCodes.IN_PROGRESS:
            errorMessage = 'Sign-in is already in progress';
            break;
          case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
            errorMessage = 'Google Play Services is not available';
            break;
          case 'auth/account-exists-with-different-credential':
            errorMessage = 'Account exists with different credential';
            break;
          case 'auth/invalid-credential':
            errorMessage = 'Invalid Google credential';
            break;
          default:
            errorMessage = err.message || errorMessage;
        }
      } else {
        errorMessage = err.message || errorMessage;
      }

      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);
      await GoogleSignin.signOut();
      const auth = getAuth(getApp());
      await auth.signOut();
      // setUser(null); // Handled by onAuthStateChanged
    } catch (err: any) {
      console.error('Sign out error:', JSON.stringify(err, null, 2));
      setError(err.message || 'Sign-out failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {user, loading, error, signIn, signOut};
};

export default GoogleAuthService;
